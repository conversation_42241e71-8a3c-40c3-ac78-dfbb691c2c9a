import React, { useEffect, useState, FormEvent } from 'react';
import apiClient from './ApiClient';
import { <PERSON><PERSON>, But<PERSON>, Form, ProgressBar, Accordion, Badge } from 'react-bootstrap';

interface FireTargetData {
  fire_target_amount: number;
  secure_withdrawal_rate: number;
  current_net_patrimoine: number;
  total_assets: number;
  total_liabilities: number;
  remaining_to_invest: number;
  potential_passive_income: number;
  progress_percentage: number;
}

interface FireSettings {
  id: number;
  fire_target_amount: number;
  secure_withdrawal_rate: number;
  update_date: string;
}

interface FirePhase {
  id: number;
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  startAge: number;
  endAge: number;
  targetAmount?: number;
  keyMilestones: string[];
  recommendedActions: string[];
  status: 'completed' | 'current' | 'upcoming';
}

interface PhaseProgress {
  currentPhase: number;
  progressInCurrentPhase: number;
  yearsToNextPhase: number;
  currentAge: number;
}

const FireSettingsForm: React.FC<{
  settings: { fire_target_amount: number; secure_withdrawal_rate: number } | null,
  onSave: () => void,
  onCancel: () => void
}> = ({ settings, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    fire_target_amount: settings?.fire_target_amount || 910150,
    secure_withdrawal_rate: (settings?.secure_withdrawal_rate || 0.04) * 100, // Convert to percentage for display
  });

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    const dataToSend = {
      fire_target_amount: formData.fire_target_amount,
      secure_withdrawal_rate: formData.secure_withdrawal_rate / 100, // Convert back to decimal
    };

    console.log('Updating FIRE settings:', dataToSend);

    try {
      const response = await apiClient.put('/fire-settings', dataToSend);
      console.log('FIRE settings updated successfully:', response.data);
      onSave();
    } catch (error: any) {
      console.error("Erreur lors de la sauvegarde des paramètres FIRE", error);
      console.error("Error details:", error.response?.data);
    }
  };

  return (
    <Form onSubmit={handleSubmit}>
      <Form.Group className="mb-3">
        <Form.Label>Objectif FIRE (€)</Form.Label>
        <Form.Control
          type="number"
          step="1000"
          value={formData.fire_target_amount}
          onChange={(e) => setFormData({ ...formData, fire_target_amount: parseFloat(e.target.value) || 0 })}
          required
        />
        <Form.Text className="text-muted">
          Montant total que vous souhaitez atteindre pour votre indépendance financière
        </Form.Text>
      </Form.Group>

      <Form.Group className="mb-3">
        <Form.Label>Taux de retrait sécurisé (%)</Form.Label>
        <Form.Control
          type="number"
          step="0.1"
          min="1"
          max="10"
          value={formData.secure_withdrawal_rate}
          onChange={(e) => setFormData({ ...formData, secure_withdrawal_rate: parseFloat(e.target.value) || 4 })}
          required
        />
        <Form.Text className="text-muted">
          Pourcentage de votre patrimoine que vous pouvez retirer annuellement (règle des 4% = 4.0)
        </Form.Text>
      </Form.Group>

      <div className="d-flex justify-content-end gap-2">
        <Button variant="secondary" onClick={onCancel}>Annuler</Button>
        <Button variant="primary" type="submit">Sauvegarder</Button>
      </div>
    </Form>
  );
};

const FirePhasesTracker: React.FC<{ currentNetPatrimoine: number }> = ({ currentNetPatrimoine }) => {
  // Données des phases FIRE basées sur le document Fire - Plan Ai Studio.md
  const firePhases: FirePhase[] = [
    {
      id: 0,
      name: "Phase 0: Bilan Patrimonial & Actions Immédiates",
      description: "Réorganisation et préparation",
      startDate: "Juin 2025",
      endDate: "Juillet 2025",
      startAge: 47,
      endAge: 47,
      keyMilestones: [
        "Apport SCPI Comète (27 000€)",
        "Optimisation PEA (72 750€ DCA Nasdaq-100)",
        "Consolidation crypto (75% BTC / 25% ETH)",
        "Sortie progressive crowdlending"
      ],
      recommendedActions: [
        "Négocier taux prêt SCPI Comète (<3,95%)",
        "Déployer DCA adaptatif sur 6 mois",
        "Simplifier portefeuille crypto",
        "Liquider positions Trading 212"
      ],
      status: 'completed'
    },
    {
      id: 1,
      name: "Phase 1: Accumulation Intensive",
      description: "Investissement mensuel de 3 415€",
      startDate: "Août 2025",
      endDate: "Fin 2037",
      startAge: 47,
      endAge: 59,
      keyMilestones: [
        "Saturation PEA (mi-2033, âge 55 ans)",
        "Fin crédit SCPI existant (Nov 2035)",
        "Atteinte objectif 910k€ (courant 2035, âge 57 ans)"
      ],
      recommendedActions: [
        "SCPI: 812€/mois + remboursements 1 303€/mois",
        "ETF Actions: 800€/mois (PUST puis SXR8)",
        "Fonds Euros: 200€/mois",
        "Crypto: 200€/mois (BTC/ETH)"
      ],
      status: 'current'
    },
    {
      id: 2,
      name: "Phase 2: Transition Pré-FIRE",
      description: "Réduction des risques et préparation",
      startDate: "Fin 2037",
      endDate: "Début 2038",
      startAge: 59,
      endAge: 60,
      targetAmount: 910150,
      keyMilestones: [
        "Capital FIRE atteint (910 150€)",
        "Réduction risque crypto (<7% patrimoine)",
        "Constitution poche ETF dividendes (30-40%)"
      ],
      recommendedActions: [
        "Arbitrer PUST/SXR8 vers ETF dividendes",
        "Sécuriser gains crypto si >7-10%",
        "Vérifier fonds d'urgence (50 000€)",
        "Optimiser fiscalité retraits"
      ],
      status: 'upcoming'
    },
    {
      id: 3,
      name: "Phase 3: Vie en Mode FIRE",
      description: "Indépendance financière confortable",
      startDate: "2038",
      endDate: "2040",
      startAge: 60,
      endAge: 62,
      targetAmount: 1200000,
      keyMilestones: [
        "FIRE Confortable (revenus > dépenses + dette)",
        "Génération 37 400€/an revenus passifs",
        "Allocation: 40% SCPI, 40% ETF, 15% Fonds Euros, 5% Crypto"
      ],
      recommendedActions: [
        "Optimiser retraits fiscaux (PFU/barème)",
        "Gérer allocation mix croissance/dividendes",
        "Suivi dépenses cibles (25 484€/an)",
        "Gestion budgétaire et CSM"
      ],
      status: 'upcoming'
    },
    {
      id: 4,
      name: "Phase 4: Évolution Post-FIRE & Retraite Légale",
      description: "FIRE pleine puissance et retraite",
      startDate: "2040",
      endDate: "2042+",
      startAge: 62,
      endAge: 64,
      keyMilestones: [
        "Fin prêt SCPI Comète (fin 2040, +10k€/an)",
        "Retraite légale (2042, âge 64 ans)",
        "Pension État (~1 800€/mois) + PER (45-55k€)"
      ],
      recommendedActions: [
        "Augmentation revenu net (+10k€/an)",
        "Intégration pension État",
        "Déblocage PER Linxea Spirit",
        "Réduction besoin puisage capital FIRE"
      ],
      status: 'upcoming'
    }
  ];

  // Calcul de la phase actuelle et progression
  const calculatePhaseProgress = (): PhaseProgress => {
    const currentYear = new Date().getFullYear();
    const birthYear = 1978; // Né le 22 septembre 1978
    const currentAge = currentYear - birthYear;

    // Détermination de la phase actuelle basée sur le patrimoine et l'âge
    let currentPhase = 1; // Par défaut Phase 1 (Accumulation Intensive)

    if (currentNetPatrimoine >= 910150) {
      if (currentAge >= 62) {
        currentPhase = 4; // Phase 4: Post-FIRE
      } else if (currentAge >= 60) {
        currentPhase = 3; // Phase 3: Vie FIRE
      } else {
        currentPhase = 2; // Phase 2: Transition Pré-FIRE
      }
    } else if (currentAge >= 59 && currentNetPatrimoine >= 800000) {
      currentPhase = 2; // Proche de la transition
    }

    // Calcul de la progression dans la phase actuelle
    let progressInCurrentPhase = 0;
    let yearsToNextPhase = 0;

    if (currentPhase === 1) {
      // Phase 1: Progression basée sur le patrimoine vers 910k€
      progressInCurrentPhase = (currentNetPatrimoine / 910150) * 100;
      // Estimation années restantes basée sur épargne mensuelle
      const monthlyInvestment = 3415;
      const remainingAmount = 910150 - currentNetPatrimoine;
      yearsToNextPhase = Math.max(0, remainingAmount / (monthlyInvestment * 12));
    } else if (currentPhase === 2) {
      // Phase 2: Progression basée sur l'âge (59-60 ans)
      progressInCurrentPhase = Math.max(0, (currentAge - 59) * 100);
      yearsToNextPhase = Math.max(0, 60 - currentAge);
    } else if (currentPhase === 3) {
      // Phase 3: Progression basée sur l'âge (60-62 ans)
      progressInCurrentPhase = Math.max(0, (currentAge - 60) * 50);
      yearsToNextPhase = Math.max(0, 62 - currentAge);
    } else if (currentPhase === 4) {
      // Phase 4: Progression basée sur l'âge (62+ ans)
      progressInCurrentPhase = Math.min(100, Math.max(0, (currentAge - 62) * 25));
      yearsToNextPhase = Math.max(0, 64 - currentAge);
    }

    return {
      currentPhase,
      progressInCurrentPhase: Math.min(100, progressInCurrentPhase),
      yearsToNextPhase,
      currentAge
    };
  };

  const phaseProgress = calculatePhaseProgress();

  // Mise à jour du statut des phases
  const phasesWithStatus = firePhases.map((phase, index) => ({
    ...phase,
    status: index < phaseProgress.currentPhase ? 'completed' as const :
            index === phaseProgress.currentPhase ? 'current' as const : 'upcoming' as const
  }));

  const getPhaseColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'current': return 'primary';
      case 'upcoming': return 'secondary';
      default: return 'secondary';
    }
  };

  const getPhaseIcon = (status: string) => {
    switch (status) {
      case 'completed': return '✅';
      case 'current': return '🔥';
      case 'upcoming': return '⏳';
      default: return '⏳';
    }
  };

  return (
    <div className="mt-4">
      <h3 className="mb-4">🎯 Tracker des Phases FIRE</h3>

      {/* Résumé de progression */}
      <div className="row mb-4">
        <div className="col-md-3">
          <div className="card text-white bg-info">
            <div className="card-body text-center">
              <h5>Phase Actuelle</h5>
              <h4>Phase {phaseProgress.currentPhase}</h4>
              <small>{phasesWithStatus[phaseProgress.currentPhase]?.name.split(':')[1] || 'Accumulation'}</small>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card text-white bg-warning">
            <div className="card-body text-center">
              <h5>Âge Actuel</h5>
              <h4>{phaseProgress.currentAge} ans</h4>
              <small>Né en septembre 1978</small>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card text-white bg-success">
            <div className="card-body text-center">
              <h5>Progression Phase</h5>
              <h4>{phaseProgress.progressInCurrentPhase.toFixed(1)}%</h4>
              <small>Dans la phase actuelle</small>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card text-white bg-danger">
            <div className="card-body text-center">
              <h5>Prochaine Phase</h5>
              <h4>{phaseProgress.yearsToNextPhase.toFixed(1)} ans</h4>
              <small>Estimation restante</small>
            </div>
          </div>
        </div>
      </div>

      {/* Timeline des phases */}
      <div className="card">
        <div className="card-header">
          <h5 className="mb-0">Timeline des Phases FIRE (2025-2042+)</h5>
        </div>
        <div className="card-body">
          <Accordion defaultActiveKey={phaseProgress.currentPhase.toString()}>
            {phasesWithStatus.map((phase, index) => (
              <Accordion.Item eventKey={index.toString()} key={phase.id}>
                <Accordion.Header>
                  <div className="d-flex align-items-center w-100">
                    <span className="me-2">{getPhaseIcon(phase.status)}</span>
                    <div className="flex-grow-1">
                      <strong>{phase.name}</strong>
                      <Badge bg={getPhaseColor(phase.status)} className="ms-2">
                        {phase.status === 'completed' ? 'Terminée' :
                         phase.status === 'current' ? 'En cours' : 'À venir'}
                      </Badge>
                    </div>
                    <small className="text-muted">
                      {phase.startDate} - {phase.endDate} (âge {phase.startAge}-{phase.endAge})
                    </small>
                  </div>
                </Accordion.Header>
                <Accordion.Body>
                  <div className="row">
                    <div className="col-md-6">
                      <h6>🎯 Jalons Clés :</h6>
                      <ul>
                        {phase.keyMilestones.map((milestone, idx) => (
                          <li key={idx}>{milestone}</li>
                        ))}
                      </ul>
                      {phase.targetAmount && (
                        <p><strong>Objectif Patrimoine :</strong> {new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(phase.targetAmount)}</p>
                      )}
                    </div>
                    <div className="col-md-6">
                      <h6>📋 Actions Recommandées :</h6>
                      <ul>
                        {phase.recommendedActions.map((action, idx) => (
                          <li key={idx}>{action}</li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  {phase.status === 'current' && (
                    <div className="mt-3">
                      <h6>Progression dans cette phase :</h6>
                      <ProgressBar
                        now={phaseProgress.progressInCurrentPhase}
                        label={`${phaseProgress.progressInCurrentPhase.toFixed(1)}%`}
                        variant={getPhaseColor(phase.status)}
                        style={{ height: '25px' }}
                      />
                      <small className="text-muted">
                        Estimation : {phaseProgress.yearsToNextPhase.toFixed(1)} années restantes
                      </small>
                    </div>
                  )}
                </Accordion.Body>
              </Accordion.Item>
            ))}
          </Accordion>
        </div>
      </div>
    </div>
  );
};

const FireTarget: React.FC = () => {
  const [data, setData] = useState<FireTargetData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [showModal, setShowModal] = useState(false);

  const fetchFireTargetData = () => {
    setLoading(true);
    setError(null);

    apiClient.get('/fire-target')
      .then(response => {
        setData(response.data);
        setLoading(false);
        setRetryCount(0);
      })
      .catch(error => {
        console.error('FireTarget API error:', error);

        if (retryCount < 2) {
          setRetryCount(prev => prev + 1);
          setTimeout(() => fetchFireTargetData(), 1000);
        } else {
          setError('Erreur lors de la récupération des données FIRE.');
          setLoading(false);
        }
      });
  };

  useEffect(() => {
    fetchFireTargetData();
  }, []);

  const handleSettingsSave = () => {
    setShowModal(false);
    fetchFireTargetData(); // Refresh data after settings update
  };

  if (loading) return <p>Chargement...</p>;
  if (error) return (
    <div>
      <p className="text-danger">{error}</p>
      <button className="btn btn-primary" onClick={fetchFireTargetData}>
        Réessayer
      </button>
    </div>
  );
  if (!data) return <p>Aucune donnée disponible.</p>;

  const formatCurrency = (value: number) => new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(value);
  const formatPercentage = (value: number) => new Intl.NumberFormat('fr-FR', { style: 'percent', minimumFractionDigits: 1 }).format(value / 100);

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1>Objectif FIRE</h1>
        <Button
          variant="primary"
          onClick={() => setShowModal(true)}
        >
          Modifier l'objectif
        </Button>
      </div>

      {/* Progression Card */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card">
            <div className="card-body">
              <h5 className="card-title">Progression vers votre Objectif FIRE</h5>
              <div className="row mb-3">
                <div className="col-md-4">
                  <div className="text-center">
                    <h3 className="text-primary">{formatCurrency(data.current_net_patrimoine)}</h3>
                    <small className="text-muted">Patrimoine Net Actuel</small>
                  </div>
                </div>
                <div className="col-md-4">
                  <div className="text-center">
                    <h3 className="text-success">{formatCurrency(data.fire_target_amount)}</h3>
                    <small className="text-muted">Objectif FIRE</small>
                  </div>
                </div>
                <div className="col-md-4">
                  <div className="text-center">
                    <h3 className="text-info">{formatPercentage(data.progress_percentage)}</h3>
                    <small className="text-muted">Progression</small>
                  </div>
                </div>
              </div>

              <ProgressBar
                now={data.progress_percentage}
                label={`${data.progress_percentage.toFixed(1)}%`}
                style={{ height: '30px' }}
                className="mb-3"
              />

              <div className="row">
                <div className="col-md-6">
                  <p className="mb-1">
                    <strong>Montant restant à investir :</strong>
                  </p>
                  <p className="text-danger fs-5">
                    {formatCurrency(Math.max(0, data.remaining_to_invest))}
                  </p>
                </div>
                <div className="col-md-6">
                  <p className="mb-1">
                    <strong>Revenu passif annuel potentiel :</strong>
                  </p>
                  <p className="text-success fs-5">
                    {formatCurrency(data.potential_passive_income)}
                  </p>
                  <small className="text-muted">
                    Montant que vous pourriez retirer annuellement de votre patrimoine actuel selon la règle des {formatPercentage(data.secure_withdrawal_rate * 100)}
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Details Cards */}
      <div className="row">
        <div className="col-md-4">
          <div className="card text-white bg-success mb-3">
            <div className="card-header">Total Actifs</div>
            <div className="card-body">
              <h5 className="card-title">{formatCurrency(data.total_assets)}</h5>
              <p className="card-text">Incluant les SCPI et tous vos investissements</p>
            </div>
          </div>
        </div>
        <div className="col-md-4">
          <div className="card text-white bg-danger mb-3">
            <div className="card-header">Total Passifs</div>
            <div className="card-body">
              <h5 className="card-title">{formatCurrency(data.total_liabilities)}</h5>
              <p className="card-text">Emprunts et dettes en cours</p>
            </div>
          </div>
        </div>
        <div className="col-md-4">
          <div className="card text-white bg-info mb-3">
            <div className="card-header">Taux de Retrait Sécurisé</div>
            <div className="card-body">
              <h5 className="card-title">{formatPercentage(data.secure_withdrawal_rate * 100)}</h5>
              <p className="card-text">Pourcentage de retrait annuel sécurisé</p>
            </div>
          </div>
        </div>
      </div>

      {/* FIRE Phases Tracker */}
      <FirePhasesTracker currentNetPatrimoine={data.current_net_patrimoine} />

      {/* Settings Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Modifier les Paramètres FIRE</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <FireSettingsForm
            settings={data ? {
              fire_target_amount: data.fire_target_amount,
              secure_withdrawal_rate: data.secure_withdrawal_rate
            } : null}
            onSave={handleSettingsSave}
            onCancel={() => setShowModal(false)}
          />
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default FireTarget;