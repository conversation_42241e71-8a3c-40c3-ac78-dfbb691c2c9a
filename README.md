# 🔥 FIRE Dashboard

[![Next.js](https://img.shields.io/badge/Next.js-15.x-black.svg)](https://nextjs.org/)
[![React](https://img.shields.io/badge/React-19.x-blue.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.x-blue.svg)](https://www.typescriptlang.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.x-green.svg)](https://fastapi.tiangolo.com/)
[![Python](https://img.shields.io/badge/Python-3.8+-green.svg)](https://www.python.org/)
[![SQLite](https://img.shields.io/badge/SQLite-3.x-lightgrey.svg)](https://www.sqlite.org/)

Une application web moderne pour le suivi et la planification de votre indépendance financière selon la méthode **FIRE** (Financial Independence, Retire Early).

## 📋 Description du Projet

FIRE Dashboard est une application complète de gestion de patrimoine et de suivi d'objectifs d'indépendance financière. Elle permet de :

- **Visualiser** votre patrimoine net en temps réel
- **Planifier** votre parcours vers l'indépendance financière
- **Suivre** vos investissements et leur répartition
- **Calculer** vos revenus passifs potentiels selon la règle des 4%
- **Gérer** tous vos actifs et passifs de manière centralisée

## ✨ Fonctionnalités Principales

### 📊 Dashboard Interactif
- Vue d'ensemble du patrimoine net total (278 791€ actuel)
- Graphiques de répartition des actifs par catégorie
- **Allocation Cible FIRE Éditable**: Modifiez directement les pourcentages cibles pour chaque catégorie d'actif dans le tableau d'allocation FIRE du Dashboard. Le total des pourcentages cibles est calculé et affiché, vous aidant à maintenir une allocation de 100%.
- Calculs automatiques des métriques FIRE
- Progression visuelle vers l'objectif d'indépendance (910 150€)
- Revenus passifs potentiels selon la règle des 4%

### 💰 Gestion Complète des Actifs
- **Comptes bancaires** : Livrets, PEA, Assurance-vie
- **Investissements boursiers** : ETF, actions individuelles
- **Crypto-actifs** : Bitcoin, Ethereum, autres cryptomonnaies
- **Prêts participatifs** : Crowdlending et investissements alternatifs
- **Catégorisation automatique** : 6 catégories d'investissement
- **CRUD complet** : Ajout, modification, suppression des actifs
- **Saisie de données cohérente** : La valeur totale d'un actif est automatiquement calculée à partir de la somme de ses catégories, rendant le champ non modifiable.
- **Interface utilisateur améliorée** : Tableau des actifs triable par nom, valeur et date de mise à jour pour une meilleure analyse.

### 🏢 Section SCPI Spécialisée
- **Gestion dédiée** des SCPI (Sociétés Civiles de Placement Immobilier)
- **4 SCPI actuelles** : EPARGNE FONCIERE, PFO2, AESTIAM, Moniwan LF OPPORTUNITE
- **Calcul automatique** des valeurs (prix × nombre de parts)
- **Total SCPI** : 81 483€ intégrés dans "Immobilier"
- **Suivi détaillé** : Prix par part (achat et vente), nombre de parts, valeur totale, dividende trimestriel.
- **Calcul des totaux** : Valeur totale du portefeuille SCPI et total des dividendes trimestriels attendus.
- **Informations Dynamiques (Web Scraping)** : Possibilité de récupérer des informations complémentaires pour une SCPI depuis SCPI Lab (type de capital, capitalisation, frais, etc.) en cliquant sur un bouton. Un champ ID SCPI Lab a été ajouté au formulaire SCPI.
- **Interface simplifiée** : Affichage optimisé n'affichant que les champs contenant des données.
- **Mise à jour automatique** : Bouton "MàJ Auto" permettant de mettre à jour tous les champs (prix, dividendes, etc.) en un clic.

### 💳 Gestion des Passifs
- **Emprunts immobiliers** : Capital restant, taux, échéances
- **Emprunt actuel** : 49 592€ à 2,2% jusqu'en novembre 2035
- **Calcul automatique** du patrimoine net (actifs - passifs)
- **Impact sur FIRE** : Prise en compte dans les projections

### 📈 Évolution Annuelle (2015-2024)
- **Historique complet** : 10 ans de données patrimoniales (2015-2024)
- **Métriques avancées** : TCAM, évolution €/%, croissance annuelle moyenne
- **Analyse de performance** : Progression de 82k€ (2015) à 257k€ (2024)
- **Visualisation temporelle** : Graphiques et tableaux détaillés (incluant Total, Evolution %, Evolution €, Croissance Annuelle Moyenne, TCAM)

### 💰 Calculateur Budget FIRE
- **25 catégories de dépenses** : Budget annuel total de 25 484€
- **Retrait brut nécessaire** : 36 406€ (incluant 30% impôts/PS)
- **Gestion des catégories** : Ajout, modification, suppression
- **Calculs automatiques** : Capital FIRE requis selon SWR

### 🎯 Tracker des Phases FIRE (2025-2042)
- **5 phases documentées** : De l'accumulation à la retraite légale
- **Phase actuelle** : Phase 1 - Accumulation Intensive (47 ans)
- **Jalons clés** : 2035 (910k€), 2038 (FIRE), 2040 (debt-free), 2042 (retraite)
- **Actions recommandées** : Stratégies spécifiques par phase
- **Progression visuelle** : 30,6% vers l'objectif FIRE

### 🔬 Calculateur de Scénarios FIRE
- **9 scénarios** : 3 SWR × 3 rendements (3,8%-4,5% × 4%-6%)
- **Matrice interactive** : Dates FIRE de 2032 à 2045 selon scénarios
- **Calculateur personnalisé** : Sliders pour SWR et rendement custom
- **Analyses comparatives** : Meilleur/pire cas vs objectif 2038
- **Recommandations** : Stratégies d'optimisation et de sécurisation

## 🏗️ Architecture Technique

### Frontend
- **Next.js 15** avec **React 19** et **TypeScript**
- **App Router** pour le routage moderne
- **Bootstrap 5** pour l'interface utilisateur
- **Tailwind CSS v3** pour les utilitaires CSS
- **Chart.js** et **react-chartjs-2** pour les graphiques
- **Axios** pour les appels API
- **Jest** pour les tests unitaires
- **ESLint** et **TypeScript** pour la qualité du code
- **Gestion de Bootstrap JS** : Utilisation d'un composant client dédié (`BootstrapClient.tsx`) pour assurer le chargement correct du JavaScript de Bootstrap dans l'environnement Next.js.
- **Cohabitation CSS** : Bootstrap 5 et Tailwind CSS v3 sont utilisés conjointement. Il est crucial d'importer Bootstrap CSS avant Tailwind CSS pour une gestion correcte des styles.

### Backend
- **FastAPI** pour l'API REST
- **SQLAlchemy** comme ORM
- **Pydantic** pour la validation des données
- **SQLite** comme base de données
- Architecture CRUD complète

### Base de Données
```
├── assets (actifs financiers)
├── liabilities (passifs et emprunts)
├── scpi (SCPI et immobilier, incluant `dividende_trimestriel`)
├── categories (catégories d'actifs)
├── asset_category (relations actifs-catégories)
├── fire_settings (paramètres FIRE configurables)
├── patrimoine_history (historique mensuel)
├── patrimoine_evolution (évolution annuelle 2015-2024)
├── budget_categories (25 catégories de dépenses FIRE)
├── depenses_reelles (dépenses réelles par catégorie)
└── fire_allocation_targets (pourcentages cibles pour l'allocation FIRE)
```

## 🚀 Installation

### Prérequis
- **Node.js** 16+ et **npm**
- **Python** 3.8+
- **Git**

### 1. Cloner le Repository
```bash
git clone https://github.com/laurentvv/fire_UI.git
cd fire_UI
```

### 2. Installation Automatique
```bash
# Exécuter le script d'installation (Windows)
./install.bat
```

Le script `install.bat` s'occupe automatiquement de :
- Configurer l'environnement virtuel Python
- Installer les dépendances backend
- Initialiser la base de données avec les données d'exemple
- Installer les dépendances frontend Next.js

### 3. Lancement de l'Application

#### Option A : Lancement automatique (Windows)
```bash
# Depuis la racine du projet
./run.bat
```

#### Option B : Lancement manuel
```bash
# Terminal 1 - Backend
cd backend
.venv\Scripts\activate
uvicorn main:app --reload

# Terminal 2 - Frontend (Next.js)
cd frontnextjs
npm run dev
```

L'application sera accessible à :
- **Frontend (Next.js)** : http://localhost:3000
- **Backend API** : http://localhost:8000
- **Documentation API** : http://localhost:8000/docs

### 4. Lancer les Tests
Pour exécuter la suite de tests pour l'application NextJS :
```bash
cd frontnextjs
npm test
```
Pour lancer les tests en mode watch :
```bash
npm run test:watch
```

### 5. Compilation pour la Production
Pour créer une version optimisée pour la production :
```bash
cd frontnextjs
npm run build
npm run start
```

### 6. Mettre à jour les données de démarrage (Seed)
Après avoir modifié les données via l'application, vous pouvez mettre à jour le fichier `seed.py` pour qu'il reflète l'état actuel de la base de données.

```bash
# Assurez-vous que le backend est en cours d'exécution
# Depuis le dossier /backend
python update_seed.py
```
Ce script interroge l'API pour récupérer les données les plus récentes et génère un nouveau fichier `backend/generated_seed.py`.
Pour utiliser ces données comme nouveau standard de démarrage :
1. Sauvegardez votre `backend/seed.py` actuel si nécessaire.
2. Remplacez `backend/seed.py` par `backend/generated_seed.py`.
Alternativement, vous pouvez exécuter `python backend/generated_seed.py` directement pour peupler une base de données avec les données capturées, sans modifier le `seed.py` original.

## 📁 Structure du Projet

```
fire_UI/
├── backend/                 # API FastAPI
│   ├── main.py             # Point d'entrée de l'API
│   ├── models.py           # Modèles SQLAlchemy
│   ├── schemas.py          # Schémas Pydantic
│   ├── crud.py             # Opérations CRUD
│   ├── database.py         # Configuration base de données
│   ├── scpi_scraper.py     # Script pour le web scraping des données SCPI
│   ├── seed.py             # Données d'exemple initiales (peut être remplacé par generated_seed.py)
│   ├── update_seed.py      # Script pour générer un nouveau fichier seed (generated_seed.py) depuis l'état actuel de la BDD via API
│   ├── generated_seed.py   # Fichier de seed généré par update_seed.py (non versionné par défaut, sauf si souhaité)
│   └── requirements.txt    # Dépendances Python
├── frontnextjs/            # Application Frontend Next.js 15
│   ├── src/
│   │   ├── app/            # Structure App Router de Next.js
│   │   │   ├── assets/     # Page gestion des actifs
│   │   │   ├── budget/     # Page calculateur budget FIRE
│   │   │   ├── evolution/  # Page historique 2015-2024
│   │   │   ├── fire/       # Page objectifs + phases tracker
│   │   │   ├── liabilities/ # Page gestion des emprunts
│   │   │   ├── scenarios/  # Page scénarios SWR/rendement
│   │   │   ├── scpi/       # Page gestion SCPI dédiée
│   │   │   ├── test/       # Page test API
│   │   │   ├── layout.tsx  # Layout principal avec navigation
│   │   │   └── page.tsx    # Dashboard - vue d'ensemble patrimoine
│   │   ├── components/     # Composants React réutilisables
│   │   │   ├── ApiClient.ts        # Client API centralisé
│   │   │   ├── Assets.tsx          # Composants gestion actifs
│   │   │   ├── BudgetFire.tsx      # Composants budget FIRE
│   │   │   ├── Dashboard.tsx       # Composants dashboard
│   │   │   ├── EvolutionAnnuelle.tsx # Composants évolution
│   │   │   ├── EvolutionChart.tsx  # Graphiques évolution
│   │   │   ├── FireTarget.tsx      # Composants objectifs FIRE
│   │   │   ├── Liabilities.tsx     # Composants emprunts
│   │   │   ├── Navbar.tsx          # Navigation principale
│   │   │   ├── SCPI.tsx            # Composants SCPI
│   │   │   └── ScenariosFire.tsx   # Composants scénarios
│   │   └── setupTests.ts   # Configuration tests Jest
│   ├── public/             # Assets statiques (images, manifest, etc.)
│   ├── next.config.ts      # Configuration Next.js
│   ├── tailwind.config.js  # Configuration Tailwind CSS
│   ├── postcss.config.js   # Configuration PostCSS
│   ├── jest.config.js      # Configuration Jest pour les tests
│   ├── eslint.config.mjs   # Configuration ESLint
│   ├── tsconfig.json       # Configuration TypeScript
│   └── package.json        # Dépendances Node.js
├── install.bat             # Script d'installation automatique
├── run.bat                 # Script de lancement Windows
└── README.md               # Documentation de ce projet
```

## 📖 Guide d'Utilisation

### 1. Premier Lancement
1. **Accédez au Dashboard** : Vue d'ensemble de votre patrimoine
2. **Configurez vos objectifs FIRE** : Définissez votre montant cible et votre SWR
3. **Ajoutez vos actifs** : Comptes, investissements, crypto-actifs
4. **Ajoutez vos SCPI** : Gestion dédiée de vos investissements immobiliers
5. **Renseignez vos emprunts** : Pour un calcul précis du patrimoine net

### 2. Navigation (NextJS App Router)
- **/** (Dashboard) : Vue d'ensemble patrimoine net (278 791€) et graphiques
- **/assets** (Patrimoine) : Gestion complète des actifs avec CRUD et tableau triable
- **/scpi** (SCPI) : Gestion spécialisée des 4 SCPI (81 483€ total)
- **/liabilities** (Emprunts) : Gestion des passifs (49 592€ emprunt immobilier)
- **/evolution** (Évolution) : Historique annuel 2015-2024 avec TCAM
- **/budget** (Budget FIRE) : Calculateur 25 catégories (25 484€ budget)
- **/scenarios** (Scénarios) : 9 combinaisons SWR/rendement pour optimisation
- **/fire** (Objectif FIRE) : Tracker 5 phases + progression vers 910 150€
- **/test** (Test API) : Outils de diagnostic et test de connectivité

### 3. Fonctionnalités Avancées
- **Calculs temps réel** : Patrimoine net, progression FIRE, revenus passifs
- **Catégorisation intelligente** : 6 catégories d'actifs automatiques
- **Métriques FIRE** : TCAM, SWR, années restantes, phases de progression
- **Analyses de scénarios** : Optimisation stratégie avec différents paramètres
- **Données documentées** : Alignement parfait avec plan FIRE personnel
- **Interface française** : Formatage monétaire et dates localisés

## 📊 Données et Métriques FIRE

### Situation Patrimoniale Actuelle (2025)
- **Patrimoine Net** : 278 791€ (actifs 328 383€ - passifs 49 592€)
- **Objectif FIRE** : 910 150€ (retrait 36 406€/an avec SWR 4%)
- **Progression** : 30,6% vers l'indépendance financière
- **Âge actuel** : 47 ans (né septembre 1978)

### Répartition des Actifs
- **Liquidité** : Comptes courants et livrets
- **Bourse** : PEA, ETF, actions (allocation principale)
- **Crypto-Actifs** : Bitcoin, Ethereum (diversification)
- **Fonds sécurisés** : Assurance-vie, fonds euros
- **Immobilier** : SCPI (81 483€) + résidence principale
- **Prêts participatifs** : Crowdlending et investissements alternatifs

### Stratégie d'Investissement
- **Capacité mensuelle** : 3 415€/mois d'investissement
- **Allocation SCPI** : 812€/mois + remboursements 1 303€/mois
- **ETF Actions** : 800€/mois (PUST puis SXR8)
- **Fonds Euros** : 200€/mois (sécurisation)
- **Crypto** : 200€/mois (BTC/ETH)

### Timeline FIRE (2025-2042)
- **2025** : Phase 1 - Accumulation Intensive (47 ans)
- **2035** : Objectif 910k€ atteint (57 ans)
- **2038** : FIRE Confortable (60 ans)
- **2040** : FIRE Pleine Puissance - fin dette (62 ans)
- **2042** : Retraite légale + pension État (64 ans)

## 📸 Captures d'Écran

### Dashboard Principal
*Vue d'ensemble avec patrimoine net, progression FIRE et répartition des actifs*

### Tracker des Phases FIRE
*Timeline 2025-2042 avec 5 phases et progression actuelle*

### Calculateur de Scénarios
*Matrice 9 scénarios SWR/rendement avec dates FIRE projetées*

### Évolution Annuelle
*Historique 2015-2024 avec TCAM et métriques de performance*

## 🔧 Configuration Avancée

### Variables d'Environnement
Créez un fichier `.env` dans le dossier `backend/` :
```env
DATABASE_URL=sqlite:///./patrimoine.db
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
```

### Personnalisation des Catégories
Les catégories d'actifs peuvent être personnalisées dans le fichier `backend/seed.py` :
- Liquidité
- Bourse
- Crypto-Actifs
- Fonds sécurisés
- Immobilier
- Prêts participatifs

## 🤝 Contribution

### Comment Contribuer
1. **Fork** le repository
2. **Créez** une branche pour votre fonctionnalité (`git checkout -b feature/nouvelle-fonctionnalite`)
3. **Committez** vos changements (`git commit -am 'Ajout d'une nouvelle fonctionnalité'`)
4. **Poussez** vers la branche (`git push origin feature/nouvelle-fonctionnalite`)
5. **Créez** une Pull Request

### Standards de Code
- **Frontend** : Utilisez TypeScript et suivez les conventions React
- **Backend** : Respectez les standards PEP 8 pour Python
- **Tests** : Ajoutez des tests pour les nouvelles fonctionnalités
- **Documentation** : Documentez les nouvelles API et composants

### Idées d'Améliorations Futures
- [ ] **Graphiques avancés** : Performance historique avec Chart.js
- [ ] **Import/Export** : CSV, Excel pour données patrimoniales
- [ ] **Notifications** : Alertes objectifs, rééquilibrage portefeuille
- [ ] **Mode sombre** : Interface adaptée pour utilisation nocturne
- [ ] **Application mobile** : React Native ou PWA
- [ ] **APIs bancaires** : Synchronisation automatique des comptes
- [ ] **Monte Carlo** : Simulations probabilistes de réussite FIRE
- [ ] **Rapports PDF** : Bilans patrimoniaux automatisés
- [ ] **Optimisation fiscale** : Calculs PFU vs barème progressif
- [ ] **Allocation dynamique** : Suggestions de rééquilibrage
- [ ] **Comparaisons** : Benchmarks vs indices de référence
- [ ] **Projections** : Modélisation inflation et évolution dépenses
- [ ] **Optimisations Next.js spécifiques** : Utilisation de `next/image` (optimisation des images), `next/font` (polices optimisées), et implémentation de rendu SSR/SSG pour les pages éligibles.
- [ ] **Extension de la couverture des tests** : Développement de tests unitaires supplémentaires et mise en place de tests d'intégration (end-to-end).
- [ ] **Améliorations qualité et performance** : Résolution des warnings TypeScript/ESLint, optimisations SEO générales.
- [ ] **Fonctionnalités avancées Next.js** : Exploration de Route Handlers (API backend Next.js) et Server Actions (mutations de données côté serveur).
- [ ] **Authentification utilisateur** : Ajout d'un système de gestion des utilisateurs et d'authentification (si multi-utilisateurs envisagé).

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🙏 Remerciements

- **React** et **TypeScript** pour le frontend moderne
- **FastAPI** pour l'API performante
- **Chart.js** pour les visualisations
- **Bootstrap** pour l'interface utilisateur
- La communauté **FIRE** pour l'inspiration

## 📞 Support

Pour toute question ou problème :
- **Issues GitHub** : [Créer une issue](https://github.com/laurentvv/fire_UI/issues)
- **Discussions** : [Discussions GitHub](https://github.com/laurentvv/fire_UI/discussions)

---

## 🎯 Objectif du Projet

Cette application a été développée pour accompagner un parcours personnel vers l'indépendance financière selon la méthode FIRE. Elle intègre une stratégie documentée avec :

- **Objectif** : 910 150€ de capital pour un retrait de 36 406€/an
- **Timeline** : FIRE en 2038 à 60 ans, retraite légale en 2042
- **Stratégie** : Investissement mensuel de 3 415€ répartis sur SCPI, ETF, crypto
- **Phases** : 5 étapes de l'accumulation à la retraite légale

L'application sert d'outil de suivi, d'analyse et d'optimisation pour maintenir le cap vers l'indépendance financière.

**Développé avec ❤️ pour la communauté FIRE française** 🇫🇷

*"The best time to plant a tree was 20 years ago. The second best time is now."* 🌱💰
