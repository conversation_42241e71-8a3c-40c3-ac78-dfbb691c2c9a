'use client';

import React, { useEffect, useState, FormEvent } from 'react';
import apiClient from '../../components/ApiClient'; // Ajusté
import { Modal, Button, Form, Alert } from 'react-bootstrap';
import EvolutionChart from '../../components/EvolutionChart'; // Ajusté

interface EvolutionData {
  id: number;
  annee: number;
  investissement: number;
  prix_part_scpi: number;
  remboursement_credit: number;
  valeur_reelle_scpi: number;
  total_patrimoine: number;
  evolution_pourcentage: number | null;
  evolution_euros: number | null;
  croissance_moyenne: number | null;
  tcam: number | null;
}

const EvolutionForm: React.FC<{
  evolution: Partial<EvolutionData> | null,
  onSave: () => void,
  onCancel: () => void
}> = ({ evolution, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    annee: evolution?.annee || new Date().getFullYear(),
    investissement: evolution?.investissement || 0,
    prix_part_scpi: evolution?.prix_part_scpi || 0,
    remboursement_credit: evolution?.remboursement_credit || 0,
    valeur_reelle_scpi: evolution?.valeur_reelle_scpi || 0,
    total_patrimoine: evolution?.total_patrimoine || 0,
  });

  useEffect(() => {
    setFormData({
        annee: evolution?.annee || new Date().getFullYear(),
        investissement: evolution?.investissement || 0,
        prix_part_scpi: evolution?.prix_part_scpi || 0,
        remboursement_credit: evolution?.remboursement_credit || 0,
        valeur_reelle_scpi: evolution?.valeur_reelle_scpi || 0,
        total_patrimoine: evolution?.total_patrimoine || 0,
    });
  }, [evolution]);

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    const method = evolution?.id ? 'put' : 'post';
    const url = evolution?.id ? `/evolution/${evolution.annee}` : '/evolution';
    try {
      await apiClient[method](url, formData);
      onSave();
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error("Erreur lors de la sauvegarde des données d&apos;évolution", errorMessage);
    }
  };

  return (
    <Form onSubmit={handleSubmit}>
      <Form.Group className="mb-3">
        <Form.Label>Année</Form.Label>
        <Form.Control type="number" min="2000" max={new Date().getFullYear() + 5} value={formData.annee} onChange={(e) => setFormData({ ...formData, annee: parseInt(e.target.value) || 0 })} required disabled={!!evolution?.id} />
      </Form.Group>
      <Form.Group className="mb-3"><Form.Label>Investissement total (€)</Form.Label><Form.Control type="number" step="0.01" value={formData.investissement} onChange={(e) => setFormData({ ...formData, investissement: parseFloat(e.target.value) || 0 })} required /></Form.Group>
      <Form.Group className="mb-3"><Form.Label>Prix moyen des parts SCPI (€)</Form.Label><Form.Control type="number" step="0.01" value={formData.prix_part_scpi} onChange={(e) => setFormData({ ...formData, prix_part_scpi: parseFloat(e.target.value) || 0 })} required /></Form.Group>
      <Form.Group className="mb-3"><Form.Label>Remboursement crédit (€)</Form.Label><Form.Control type="number" step="0.01" value={formData.remboursement_credit} onChange={(e) => setFormData({ ...formData, remboursement_credit: parseFloat(e.target.value) || 0 })} required /></Form.Group>
      <Form.Group className="mb-3"><Form.Label>Valeur réelle SCPI (€)</Form.Label><Form.Control type="number" step="0.01" value={formData.valeur_reelle_scpi} onChange={(e) => setFormData({ ...formData, valeur_reelle_scpi: parseFloat(e.target.value) || 0 })} required /></Form.Group>
      <Form.Group className="mb-3"><Form.Label>Total patrimoine (€)</Form.Label><Form.Control type="number" step="0.01" value={formData.total_patrimoine} onChange={(e) => setFormData({ ...formData, total_patrimoine: parseFloat(e.target.value) || 0 })} required /></Form.Group>
      <Alert variant="info"><small>Les métriques d&apos;évolution (%, €, TCAM) seront calculées automatiquement après la sauvegarde.</small></Alert>
      <div className="d-flex justify-content-end gap-2"><Button variant="secondary" onClick={onCancel}>Annuler</Button><Button variant="primary" type="submit">Sauvegarder</Button></div>
    </Form>
  );
};

const EvolutionAnnuellePage: React.FC = () => {
  const [evolutions, setEvolutions] = useState<EvolutionData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [selectedEvolution, setSelectedEvolution] = useState<Partial<EvolutionData> | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [fireTargetAmount, setFireTargetAmount] = useState<number | null>(null);

  const fetchFireTarget = async () => {
    try {
      const response = await apiClient.get('/fire-settings');
      if (response.data && response.data.fire_target_amount !== undefined) {
        setFireTargetAmount(response.data.fire_target_amount);
      } else {
        const defaultTargetResponse = await apiClient.get('/fire-target'); // Vérifier si ce endpoint existe ou est pertinent
        setFireTargetAmount(defaultTargetResponse.data.fire_target_amount);
      }
    } catch (error) {
      console.error("Erreur lors de la récupération de l'objectif FIRE", error);
      setFireTargetAmount(910150); // Fallback à une valeur par défaut si l'API échoue
    }
  };

  const fetchEvolutions = () => {
    setLoading(true);
    setError(null);
    apiClient.get('/evolution')
      .then(response => {
        setEvolutions(response.data);
        setLoading(false);
        setRetryCount(0);
      })
      .catch(error => {
        console.error('Evolution API error:', error);
        if (retryCount < 2) {
          setRetryCount(prev => prev + 1);
          setTimeout(() => fetchEvolutions(), 1000);
        } else {
          setError('Erreur lors de la récupération des données d\'évolution.');
          setLoading(false);
        }
      });
  };

  useEffect(() => {
    fetchEvolutions();
    fetchFireTarget();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSave = () => {
    setShowModal(false);
    setSelectedEvolution(null);
    fetchEvolutions();
  };

  const handleDelete = async (annee: number) => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer les données de ${annee} ?`)) {
      try {
        await apiClient.delete(`/evolution/${annee}`);
        fetchEvolutions();
      } catch (error) {
        console.error("Erreur lors de la suppression des données d'évolution", error);
      }
    }
  };

  const formatCurrency = (value: number | null) => {
    if (value === null || value === undefined) return 'N/A';
    return new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(value);
  };

  const formatPercentage = (value: number | null) => {
    if (value === null || value === undefined) return 'N/A';
    return new Intl.NumberFormat('fr-FR', { style: 'percent', minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(value / 100);
  };

  if (loading && !showModal && retryCount === 0) return <p>Chargement des données d&apos;évolution...</p>;
  if (error && retryCount >=2) return (
    <div>
      <p className="text-danger">{error}</p>
      <button className="btn btn-primary" onClick={() => {setRetryCount(0); fetchEvolutions();}}>Réessayer</button>
    </div>
  );
  if (!loading && !evolutions.length && !error) return <p>Aucune donnée d&apos;évolution trouvée.</p>;
  if (loading && !showModal) return <p>Chargement des données d&apos;évolution, tentative {retryCount + 1}...</p>;

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1>Évolution Annuelle du Patrimoine</h1>
        <Button variant="primary" onClick={() => { setSelectedEvolution({}); setShowModal(true); }}>Ajouter une année</Button>
      </div>

      {evolutions.length > 0 && (
        <div className="mb-4">
          <div className="row">
            <div className="col-md-3"><div className="card text-white bg-primary"><div className="card-body text-center"><h5>Période</h5><h4>{evolutions[0]?.annee} - {evolutions[evolutions.length - 1]?.annee}</h4></div></div></div>
            <div className="col-md-3"><div className="card text-white bg-success"><div className="card-body text-center"><h5>Croissance Totale</h5><h4>{formatCurrency(evolutions[evolutions.length - 1]?.total_patrimoine - evolutions[0]?.total_patrimoine)}</h4></div></div></div>
            <div className="col-md-3"><div className="card text-white bg-info"><div className="card-body text-center"><h5>TCAM Moyen</h5><h4>{formatPercentage(evolutions[evolutions.length - 1]?.tcam)}</h4></div></div></div>
            <div className="col-md-3"><div className="card text-white bg-warning"><div className="card-body text-center"><h5>Patrimoine Actuel</h5><h4>{formatCurrency(evolutions[evolutions.length - 1]?.total_patrimoine)}</h4></div></div></div>
          </div>
        </div>
      )}

      { evolutions.length > 0 &&
        <div className="table-responsive">
          <table className="table table-striped table-hover">
            <thead className="table-dark"><tr><th>Année</th><th className="text-end">Total Patrimoine</th><th className="text-end">Evolution en %</th><th className="text-end">Evolution en €</th><th className="text-end">Croissance Annuel Moyen (€)</th><th className="text-end">TCAM (%)</th><th className="text-center">Actions</th></tr></thead>
            <tbody>
              {evolutions.map(evolution => (
                <tr key={evolution.id}>
                  <td><strong>{evolution.annee}</strong></td>
                  <td className="text-end"><strong>{formatCurrency(evolution.total_patrimoine)}</strong></td>
                  <td className={`text-end ${(evolution.evolution_pourcentage || 0) >= 0 ? 'text-success' : 'text-danger'}`}>{formatPercentage(evolution.evolution_pourcentage)}</td>
                  <td className={`text-end ${(evolution.evolution_euros || 0) >= 0 ? 'text-success' : 'text-danger'}`}>{formatCurrency(evolution.evolution_euros)}</td>
                  <td className="text-end">{formatCurrency(evolution.croissance_moyenne)}</td>
                  <td className="text-end text-info">{formatPercentage(evolution.tcam)}</td>
                  <td className="text-center">
                    <Button variant="outline-primary" size="sm" className="me-2" onClick={() => { setSelectedEvolution(evolution); setShowModal(true); }}>Modifier</Button>
                    <Button variant="outline-danger" size="sm" onClick={() => handleDelete(evolution.annee)}>Supprimer</Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      }

      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg"><Modal.Header closeButton><Modal.Title>{selectedEvolution?.id ? `Modifier ${selectedEvolution.annee}` : 'Ajouter une année'}</Modal.Title></Modal.Header><Modal.Body><EvolutionForm evolution={selectedEvolution} onSave={handleSave} onCancel={() => setShowModal(false)} /></Modal.Body></Modal>

      {evolutions.length > 0 && fireTargetAmount !== null && (
        <EvolutionChart evolutionData={evolutions} fireTargetAmount={fireTargetAmount} />
      )}

      <div className="mt-5 p-4 bg-light rounded"><h4>Comprendre les métriques d&apos;évolution</h4><hr /><dl className="row"><dt className="col-sm-4">TCAM (Taux de Croissance Annuel Moyen)</dt><dd className="col-sm-8"><p>Le TCAM représente le taux de croissance annuel composé moyen de votre patrimoine sur une période donnée (depuis 2015 dans ce tableau). Il lisse les fluctuations annuelles pour donner une idée de la performance moyenne à long terme.</p><p><em>Formule :</em> <code>((Valeur Finale / Valeur Initiale)^(1 / Nombre d&apos;Années)) - 1</code></p><p>Un TCAM élevé indique une croissance robuste et constante de votre patrimoine au fil du temps.</p></dd><dt className="col-sm-4 mt-3">Evolution en %</dt><dd className="col-sm-8 mt-3"><p>Cette valeur montre la variation en pourcentage du patrimoine total par rapport à l&apos;année précédente.</p><p><em>Formule :</em> <code>((Total Patrimoine Année N - Total Patrimoine Année N-1) / Total Patrimoine Année N-1) * 100</code></p><p>Elle permet d&apos;identifier rapidement les années de forte croissance ou de stagnation.</p></dd><dt className="col-sm-4 mt-3">Evolution en €</dt><dd className="col-sm-8 mt-3"><p>Indique l&apos;augmentation (ou la diminution) absolue du patrimoine total en euros par rapport à l&apos;année précédente.</p><p><em>Formule :</em> <code>Total Patrimoine Année N - Total Patrimoine Année N-1</code></p><p>Cette métrique donne une mesure concrète de la richesse créée (ou perdue) chaque année.</p></dd><dt className="col-sm-4 mt-3">Croissance Annuel Moyen (€)</dt><dd className="col-sm-8 mt-3"><p>Représente la moyenne simple de l&apos;augmentation annuelle de votre patrimoine en euros depuis l&apos;année de base (2015).</p><p><em>Formule :</em> <code>(Total Patrimoine Année N - Total Patrimoine 2015) / (Année N - 2015)</code></p><p>Elle donne une indication de la contribution monétaire moyenne annuelle à la croissance de votre patrimoine depuis le début de la période de suivi.</p></dd></dl></div>
    </div>
  );
};

export default EvolutionAnnuellePage;
