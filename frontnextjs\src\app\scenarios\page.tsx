'use client';

import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Form, Table, Badge, Alert } from 'react-bootstrap';
import apiClient from '../../components/ApiClient'; // Ajusté

interface ScenarioResult {
  swr: number;
  returnRate: number;
  requiredCapital: number;
  yearsToFire: number;
  fireDate: number;
  ageAtFire: number;
  monthlyInvestmentNeeded: number;
  status: 'early' | 'ontime' | 'delayed';
}

interface FireData {
  current_net_patrimoine: number;
  fire_target_amount: number; // Bien que non utilisé directement dans les calculs ici, il est bon de l'avoir
}

const ScenariosFirePage: React.FC = () => {
  const [fireData, setFireData] = useState<FireData | null>(null);
  const [customSwr, setCustomSwr] = useState(4.0);
  const [customReturn, setCustomReturn] = useState(5.0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);


  const CURRENT_YEAR = 2025;
  const BIRTH_YEAR = 1978;
  const TARGET_YEAR = 2038;
  const ANNUAL_GROSS_WITHDRAWAL = 36406;
  const MONTHLY_INVESTMENT_CAPACITY = 3415;

  const swrScenarios = [
    { name: 'Conservateur', rate: 3.8, description: 'Sécurité maximale' },
    { name: 'Standard', rate: 4.0, description: 'Règle classique' },
    { name: 'Agressif', rate: 4.5, description: 'Optimiste' }
  ];

  const returnScenarios = [
    { name: 'Conservateur', rate: 4.0, description: 'Marché difficile' },
    { name: 'Modéré', rate: 5.0, description: 'Hypothèse actuelle' },
    { name: 'Optimiste', rate: 6.0, description: 'Marché favorable' }
  ];

  useEffect(() => {
    const fetchFireData = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await apiClient.get('/fire-target');
        setFireData(response.data);
      } catch (err) {
        console.error('Error fetching FIRE data:', err);
        setError("Erreur lors du chargement des données de base FIRE.");
      } finally {
        setLoading(false);
      }
    };
    fetchFireData();
  }, []);

  const calculateScenario = (swr: number, returnRate: number): ScenarioResult => {
    if (!fireData) {
      return { swr, returnRate, requiredCapital: 0, yearsToFire: 0, fireDate: 0, ageAtFire: 0, monthlyInvestmentNeeded: 0, status: 'delayed' };
    }

    const requiredCapital = ANNUAL_GROSS_WITHDRAWAL / (swr / 100);
    const remainingToInvest = Math.max(0, requiredCapital - fireData.current_net_patrimoine);
    let yearsToFire = 0;

    if (remainingToInvest > 0) {
      const R = returnRate / 100; // Taux de rendement annuel
      const P = MONTHLY_INVESTMENT_CAPACITY * 12; // Paiement annuel
      const PV = fireData.current_net_patrimoine; // Valeur actuelle
      const FV = requiredCapital; // Valeur future (capital requis)

      if (R > 0) {
        // Formule complexe pour NPER avec investissements réguliers et capital initial
        // N = log((FV*R + P) / (PV*R + P)) / log(1+R)
        const numerator = Math.log((FV * R + P) / (PV * R + P));
        const denominator = Math.log(1 + R);
        if (denominator === 0 || (PV * R + P) <= 0 || (FV * R + P) <=0 ) { // Eviter division par zéro ou log de non-positif
            yearsToFire = P > 0 ? remainingToInvest / P : Infinity; // Si pas de rendement ou problème de calcul
        } else {
            yearsToFire = numerator / denominator;
        }

      } else if (P > 0) { // Si pas de rendement, calcul linéaire
        yearsToFire = remainingToInvest / P;
      } else { // Si pas de rendement et pas d'investissement, impossible d'atteindre
        yearsToFire = Infinity;
      }
    }

    yearsToFire = Math.max(0, yearsToFire); // S'assurer que ce n'est pas négatif

    const fireDate = CURRENT_YEAR + yearsToFire;
    const ageAtFire = (CURRENT_YEAR - BIRTH_YEAR) + yearsToFire;

    let monthlyInvestmentNeeded = 0;
    const yearsToTarget = TARGET_YEAR - CURRENT_YEAR;
    if (yearsToTarget > 0) {
        const R_monthly = returnRate / 100 / 12;
        const N_months = yearsToTarget * 12;
        const PV = fireData.current_net_patrimoine;
        const FV_target = requiredCapital;

        if (R_monthly > 0) {
            const futureValueOfPV = PV * Math.pow(1 + R_monthly, N_months);
            const neededFromInvestments = FV_target - futureValueOfPV;
            if (neededFromInvestments > 0) {
                monthlyInvestmentNeeded = (neededFromInvestments * R_monthly) / (Math.pow(1 + R_monthly, N_months) - 1);
            } else {
                monthlyInvestmentNeeded = 0; // L'objectif est atteint sans investissements supplémentaires
            }
        } else if (N_months > 0) { // Pas de rendement, investissement linéaire
            const neededFromInvestments = FV_target - PV;
             if (neededFromInvestments > 0) {
                monthlyInvestmentNeeded = neededFromInvestments / N_months;
            } else {
                 monthlyInvestmentNeeded = 0;
            }
        } else {
            monthlyInvestmentNeeded = (FV_target > PV) ? Infinity : 0; // Cible déjà passée ou impossible sans temps/rendement
        }
    }
    monthlyInvestmentNeeded = Math.max(0, monthlyInvestmentNeeded);


    let status: 'early' | 'ontime' | 'delayed' = 'ontime';
    if (fireDate < TARGET_YEAR - 0.5) status = 'early';
    else if (fireDate > TARGET_YEAR + 0.5) status = 'delayed';

    return { swr, returnRate, requiredCapital, yearsToFire, fireDate, ageAtFire, monthlyInvestmentNeeded, status };
  };

  const calculateCustomScenario = (): ScenarioResult => calculateScenario(customSwr, customReturn);

  const formatCurrency = (value: number) => new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR', maximumFractionDigits: 0 }).format(value);
  const formatYear = (year: number) => (isFinite(year) && year > 0) ? year.toFixed(1) : "N/A";
  const formatAge = (age: number) => (isFinite(age) && age > 0) ? age.toFixed(1) : "N/A";


  const getStatusColor = (status: string) => ({ early: 'success', ontime: 'primary', delayed: 'danger' }[status] || 'secondary');
  const getStatusText = (status: string) => ({ early: 'En avance', ontime: 'Dans les temps', delayed: 'En retard' }[status] || 'Inconnu');

  if (loading) return <p>Chargement des données de base...</p>;
  if (error || !fireData) return <Alert variant="danger">{error || "Données FIRE non disponibles."}</Alert>;

  const allScenarios: ScenarioResult[] = swrScenarios.flatMap(swr => returnScenarios.map(ret => calculateScenario(swr.rate, ret.rate)));
  const customScenario = calculateCustomScenario();

  const validScenarios = allScenarios.filter(s => isFinite(s.fireDate));
  const bestScenario = validScenarios.length > 0 ? validScenarios.reduce((best, current) => current.fireDate < best.fireDate ? current : best) : customScenario;
  const worstScenario = validScenarios.length > 0 ? validScenarios.reduce((worst, current) => current.fireDate > worst.fireDate ? current : worst) : customScenario;

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4"><h1>🎯 Calculateur de Scénarios FIRE</h1></div>
      <Row className="mb-4">
        <Col md={4}><Card className="text-white bg-success"><Card.Body className="text-center"><h5>Meilleur Scénario</h5><h4>{formatYear(bestScenario.fireDate)}</h4><small>SWR {bestScenario.swr}% • Rendement {bestScenario.returnRate}%</small></Card.Body></Card></Col>
        <Col md={4}><Card className="text-white bg-primary"><Card.Body className="text-center"><h5>Objectif Documenté</h5><h4>{TARGET_YEAR}</h4><small>SWR 4% • Rendement 5% • Âge 60 ans</small></Card.Body></Card></Col>
        <Col md={4}><Card className="text-white bg-danger"><Card.Body className="text-center"><h5>Pire Scénario</h5><h4>{formatYear(worstScenario.fireDate)}</h4><small>SWR {worstScenario.swr}% • Rendement {worstScenario.returnRate}%</small></Card.Body></Card></Col>
      </Row>
      <Alert variant="info" className="mb-4"><Alert.Heading>Paramètres de Base</Alert.Heading><Row>
          <Col md={3}><strong>Patrimoine actuel :</strong><br />{formatCurrency(fireData.current_net_patrimoine)}</Col>
          <Col md={3}><strong>Retrait annuel brut :</strong><br />{formatCurrency(ANNUAL_GROSS_WITHDRAWAL)}</Col>
          <Col md={3}><strong>Investissement mensuel :</strong><br />{formatCurrency(MONTHLY_INVESTMENT_CAPACITY)}</Col>
          <Col md={3}><strong>Objectif documenté :</strong><br />{TARGET_YEAR} (âge {(TARGET_YEAR - CURRENT_YEAR) + (CURRENT_YEAR - BIRTH_YEAR)} ans)</Col>
      </Row></Alert>
      <Card className="mb-4"><Card.Header><h5>🔧 Calculateur Personnalisé</h5></Card.Header><Card.Body>
          <Row>
            <Col md={6}><Form.Group className="mb-3"><Form.Label>Taux de Retrait Sécurisé (SWR) : {customSwr}%</Form.Label><Form.Range min={3} max={6} step={0.1} value={customSwr} onChange={(e) => setCustomSwr(parseFloat(e.target.value))} /><div className="d-flex justify-content-between"><small>3% (Très conservateur)</small><small>6% (Très agressif)</small></div></Form.Group></Col>
            <Col md={6}><Form.Group className="mb-3"><Form.Label>Rendement Annuel Attendu : {customReturn}%</Form.Label><Form.Range min={3} max={8} step={0.1} value={customReturn} onChange={(e) => setCustomReturn(parseFloat(e.target.value))} /><div className="d-flex justify-content-between"><small>3% (Très conservateur)</small><small>8% (Très optimiste)</small></div></Form.Group></Col>
          </Row>
          <Row>
            <Col md={3}><div className="text-center"><h6>Capital Requis</h6><h4 className="text-primary">{formatCurrency(customScenario.requiredCapital)}</h4></div></Col>
            <Col md={3}><div className="text-center"><h6>Date FIRE</h6><h4 className="text-success">{formatYear(customScenario.fireDate)}</h4></div></Col>
            <Col md={3}><div className="text-center"><h6>Âge FIRE</h6><h4 className="text-info">{formatAge(customScenario.ageAtFire)} ans</h4></div></Col>
            <Col md={3}><div className="text-center"><h6>Statut vs {TARGET_YEAR}</h6><Badge bg={getStatusColor(customScenario.status)} className="fs-6">{getStatusText(customScenario.status)}</Badge></div></Col>
          </Row>
      </Card.Body></Card>
      <Card className="mb-4"><Card.Header><h5>📊 Matrice des Scénarios (Date FIRE)</h5></Card.Header><Card.Body>
          <Table responsive bordered className="text-center">
            <thead><tr><th>SWR \ Rendement</th>{returnScenarios.map(ret => (<th key={ret.rate} className="bg-light">{ret.name}<br /><small>{ret.rate}%</small></th>))}</tr></thead>
            <tbody>{swrScenarios.map(swr => (<tr key={swr.rate}><td className="bg-light"><strong>{swr.name}</strong><br /><small>{swr.rate}%</small></td>{returnScenarios.map(ret => { const scenario = allScenarios.find(s => s.swr === swr.rate && s.returnRate === ret.rate); return (<td key={`${swr.rate}-${ret.rate}`} className={`table-${getStatusColor(scenario?.status || 'secondary')}`}><strong>{formatYear(scenario?.fireDate || 0)}</strong><br /><small>{formatAge(scenario?.ageAtFire || 0)} ans</small></td>);})}</tr>))}</tbody>
          </Table>
          <div className="mt-2"><Badge bg="success" className="me-2">En avance (avant {TARGET_YEAR - 0.5})</Badge><Badge bg="primary" className="me-2">Dans les temps ({TARGET_YEAR - 0.5}-{TARGET_YEAR + 0.5})</Badge><Badge bg="danger">En retard (après {TARGET_YEAR + 0.5})</Badge></div>
      </Card.Body></Card>
      <Card><Card.Header><h5>📋 Analyse Détaillée des Scénarios</h5></Card.Header><Card.Body>
          <Table responsive striped hover>
            <thead><tr><th>SWR</th><th>Rendement</th><th>Capital Requis</th><th>Date FIRE</th><th>Âge FIRE</th><th>Années Restantes</th><th>Investissement Mensuel pour {TARGET_YEAR}</th><th>Statut</th></tr></thead>
            <tbody>{allScenarios.sort((a, b) => a.fireDate - b.fireDate).map((scenario, index) => (
                <tr key={index}><td>{scenario.swr}%</td><td>{scenario.returnRate}%</td><td>{formatCurrency(scenario.requiredCapital)}</td><td><strong>{formatYear(scenario.fireDate)}</strong></td><td>{formatAge(scenario.ageAtFire)} ans</td><td>{formatYear(scenario.yearsToFire)} ans</td>
                  <td>{scenario.monthlyInvestmentNeeded > 0 ? formatCurrency(scenario.monthlyInvestmentNeeded) : <span className="text-success">Objectif déjà atteignable</span>}</td>
                  <td><Badge bg={getStatusColor(scenario.status)}>{getStatusText(scenario.status)}</Badge></td>
                </tr>))}
            </tbody>
          </Table>
      </Card.Body></Card>
      <Alert variant="warning" className="mt-4"><Alert.Heading>💡 Analyse et Recommandations</Alert.Heading><Row>
          <Col md={6}><h6>Observations clés :</h6><ul><li><strong>Scénario actuel (4% SWR, 5% rendement) :</strong> FIRE en {formatYear(allScenarios.find(s => s.swr === 4.0 && s.returnRate === 5.0)?.fireDate || TARGET_YEAR)}</li><li><strong>Meilleur cas :</strong> FIRE dès {formatYear(bestScenario.fireDate)} avec SWR {bestScenario.swr}% et rendement {bestScenario.returnRate}%</li><li><strong>Pire cas :</strong> FIRE retardé à {formatYear(worstScenario.fireDate)} avec SWR {worstScenario.swr}% et rendement {worstScenario.returnRate}%</li></ul></Col>
          <Col md={6}><h6>Stratégies d&apos;optimisation :</h6><ul><li><strong>Pour accélérer :</strong> Viser un rendement de 6% ou accepter un SWR de 4.5%</li><li><strong>Pour sécuriser :</strong> Prévoir un SWR conservateur de 3.8% (capital requis : {formatCurrency(ANNUAL_GROSS_WITHDRAWAL / 0.038)})</li><li><strong>Flexibilité :</strong> Ajuster l&apos;allocation d&apos;actifs selon les conditions de marché</li></ul></Col>
      </Row></Alert>
    </div>
  );
};

export default ScenariosFirePage;
