# Documentation de la Section SCPI

## Résumé des améliorations

La section SCPI a été améliorée avec les fonctionnalités suivantes :

1. **Scraper SCPI avancé** : Un nouveau scraper a été développé pour extraire automatiquement les données des SCPI depuis le site scpi-lab.com. Ce scraper utilise Selenium pour naviguer sur le site et extraire des informations détaillées sur chaque SCPI.

2. **Modèle de données enrichi** : Le modèle de données SCPI a été étendu pour inclure de nombreuses informations supplémentaires :
   - Informations générales (société de gestion, type de capital, type d'actifs, etc.)
   - Performance et valorisation (TDVM, taux d'occupation, valeur de reconstitution, etc.)
   - Données trimestrielles (collecte brute/nette, acompte du dernier trimestre, etc.)

3. **Interface utilisateur améliorée** : L'interface utilisateur a été mise à jour pour afficher ces nouvelles informations et permettre des actions comme :
   - Consultation des données détaillées d'une SCPI
   - Mise à jour automatique des données depuis SCPI Lab
   - Affichage des performances et statistiques clés

4. **Automatisation de la mise à jour** : Un script d'installation a été créé pour faciliter la mise à jour de la base de données et des dépendances.

5. **Gestion améliorée des prix de vente SCPI** : Le système distingue maintenant clairement entre le prix d'achat (price_per_share) et le prix de vente (prix_part_vente) des parts SCPI, permettant une valorisation plus précise du patrimoine et préservant les valeurs personnalisées lors des mises à jour automatiques.

## Étapes pour mettre à jour l'application

1. **Exécuter le script d'installation** :
   - Double-cliquez sur le fichier `update_scpi.bat` à la racine du projet
   - Ce script va :
     - Mettre à jour les dépendances Python (notamment selenium pour le scraper)
     - Recréer la base de données avec la structure correcte
     - Afficher un message de confirmation une fois terminé

2. **Vérifier l'installation** :
   - Lancez l'application avec `run.bat`
   - Naviguez vers la section SCPI pour vérifier que tout fonctionne correctement

## Nouvelles fonctionnalités

### 1. Consultation des données détaillées

Pour chaque SCPI, vous pouvez maintenant :
- Cliquer sur le bouton "Consulter" pour voir les données détaillées extraites de SCPI Lab
- Ces données incluent des informations sur la performance, la valorisation, et les dernières actualités

### 2. Mise à jour automatique

Pour mettre à jour une SCPI avec les dernières données :
- Assurez-vous que l'ID SCPI Lab est correctement configuré
- Cliquez sur le bouton "MàJ Auto"
- Les données seront automatiquement extraites et mises à jour dans la base de données

### 3. Ajout de nouvelles SCPI

Pour ajouter une nouvelle SCPI :
- Cliquez sur "Ajouter une SCPI"
- Remplissez les informations de base
- Si vous connaissez l'ID SCPI Lab, ajoutez-le pour permettre les mises à jour automatiques
- Les ID SCPI Lab peuvent être trouvés dans l'URL de la page SCPI sur scpi-lab.com

## Problèmes connus et solutions

### 1. Erreur d'hydratation React

**Problème** : Une erreur d'hydratation peut apparaître dans la console du navigateur :
```
Error: A tree hydrated but some attributes of the server rendered HTML didn't match the client properties.
```

**Solution** : Cette erreur est liée à un problème de rendu côté serveur vs client dans Next.js. Elle n'affecte pas le fonctionnement de l'application et peut être ignorée. Si elle persiste, essayez de :
- Vider le cache du navigateur
- Redémarrer le serveur de développement

### 2. Échec du scraping

**Problème** : Le scraping peut échouer si le site SCPI Lab change sa structure ou si la connexion internet est instable.

**Solution** :
- Vérifiez votre connexion internet
- Assurez-vous que l'ID SCPI Lab est correct
- Réessayez plus tard, car le site peut être temporairement indisponible
- Si le problème persiste, mettez à jour manuellement les informations de la SCPI

### 3. Chrome/ChromeDriver non trouvé

**Problème** : Le scraper peut signaler que Chrome ou ChromeDriver n'est pas trouvé.

**Solution** :
- Vérifiez que les fichiers existent dans les dossiers `scaper_new/chrome-win64/chrome.exe` et `scaper_new/chromedriver-win64/chromedriver.exe`
- Si nécessaire, téléchargez manuellement Chrome et ChromeDriver et placez-les dans ces dossiers
- Assurez-vous que les versions de Chrome et ChromeDriver sont compatibles
### 4. Gestion du prix de vente des parts SCPI

**Problème** : Auparavant, le système ne gérait pas correctement le prix de vente des parts SCPI (prix_part_vente), ce qui pouvait entraîner des calculs incorrects de la valeur totale des SCPI lors des mises à jour automatiques.

**Solution** :
- Un nouveau champ `prix_part_vente` a été ajouté au modèle SCPI pour stocker spécifiquement le prix de vente/retrait des parts.
- Le scraper a été amélioré pour extraire cette valeur depuis SCPI Lab avec une approche plus robuste :
  - Une valeur par défaut est utilisée en cas d'échec de l'extraction
  - Des vérifications supplémentaires sont effectuées pour s'assurer de la validité de la valeur extraite
- La logique de mise à jour a été renforcée pour :
  - Préserver les valeurs personnalisées de `prix_part_vente` lors des mises à jour automatiques
  - N'utiliser la valeur scrapée que si aucune valeur personnalisée n'existe
  - Utiliser `prix_part_vente` pour le calcul de la valeur totale si disponible, sinon utiliser `price_per_share`
- Le fichier seed.py a été mis à jour pour inclure ce champ dans les données initiales

**Impact** : Cette amélioration permet une gestion plus précise des SCPI, notamment pour celles dont le prix de vente diffère significativement du prix d'achat, ce qui est courant pour les SCPI à capital variable. Les calculs de valorisation du patrimoine sont désormais plus précis.

## Maintenance future

Pour maintenir la section SCPI à jour :


1. **Mises à jour régulières** : Exécutez régulièrement le script `update_scpi.bat` pour vous assurer que toutes les dépendances sont à jour.

2. **Surveillance des changements de SCPI Lab** : Si le site SCPI Lab change sa structure, le scraper devra être mis à jour en conséquence.

3. **Ajout de nouvelles SCPI** : Lorsque vous investissez dans une nouvelle SCPI, ajoutez-la à l'application et configurez son ID SCPI Lab pour permettre les mises à jour automatiques.