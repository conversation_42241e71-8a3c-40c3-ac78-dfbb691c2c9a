const nextJest = require('next/jest')

// Fournir le chemin vers votre application Next.js pour charger next.config.js et les variables .env dans votre environnement de test
const createJestConfig = nextJest({
  dir: './',
})

// Ajouter toute configuration personnalisée à être passée à Jest
const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'], // Utilise notre fichier existant
  testEnvironment: 'jest-environment-jsdom',
  // si vous utilisez TypeScript avec un path baseUrl défini dans tsconfig.json, vous aurez besoin de moduleNameMapper ici
  // moduleNameMapper: {
  //   '^@/components/(.*)$': '<rootDir>/src/components/$1',
  //   '^@/app/(.*)$': '<rootDir>/src/app/$1',
  // },
  // Exemples de patterns à ignorer si vous avez des fichiers non-test dans les dossiers de test
  // testPathIgnorePatterns: ['<rootDir>/.next/', '<rootDir>/node_modules/'],
  // transformIgnorePatterns: ['/node_modules/', '^.+\\.module\\.(css|sass|scss)$'],
}

// createJestConfig est exporté de cette façon pour s'assurer que next/jest peut charger la configuration Next.js
module.exports = createJestConfig(customJestConfig)
