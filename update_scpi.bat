@echo off
echo ===== Mise a jour de la section SCPI =====
echo.

cd backend

ECHO Activation de l'environnement virtuel...
call .venv\Scripts\activate
if %errorlevel% neq 0 (
    ECHO ERREUR: Impossible d'activer l'environnement virtuel
    pause
    exit /b 1
)

echo 1. Mise a jour des dependances Python...
pip install -r requirements.txt
if %ERRORLEVEL% NEQ 0 (
    echo Erreur lors de l'installation des dependances.
    pause
    exit /b 1
)
echo Dependances mises a jour avec succes.
echo.

echo 2. Recreation de la base de donnees...
python seed.py
if %ERRORLEVEL% NEQ 0 (
    echo Erreur lors de la recreation de la base de donnees.
    cd ..
    pause
    exit /b 1
)
cd ..
echo Base de donnees recreee avec succes.
echo.

echo ===== Mise a jour terminee avec succes =====
echo La section SCPI a ete mise a jour avec les dernieres ameliorations.
echo Vous pouvez maintenant lancer l'application avec run.bat
echo.
pause