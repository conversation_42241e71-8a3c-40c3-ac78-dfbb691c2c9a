# Projet Fire UI - Brief du projet

## Objectif
Le but de ce projet est de créer une application web pour aider les utilisateurs à atteindre la liberté financière (Financial Independence Retire Early, ou FIRE). Cette application permettra aux utilisateurs d'entrer leurs données financières, de simuler différents scénarios et de visualiser l'évolution de leur patrimoine dans le temps.

## Fonctionnalités principales
- Interface utilisateur intuitive pour entrer les actifs, passifs et revenus
- Simulation des scénarios financiers (FIRE)
- Visualisation des tendances annuelles du patrimoine, incluant des métriques clés comme le TCAM (Taux de Croissance Annuel Moyen), l'évolution en valeur et en pourcentage, et la croissance moyenne.
- Calculs basés sur les données fournies par les utilisateurs

## Livrables attendus
- Application web entièrement fonctionnelle.
- Code source bien structuré et documenté.
- Tests unitaires pour assurer la qualité du code.

## Dépendances
- Backend en Python (FastAPI).
- Frontend en React.js. (Note : Actuellement en cours de migration vers Next.js pour améliorer les performances, l'expérience de développement et les fonctionnalités).

## Contraintes techniques
- Utilisation des technologies actuelles et reconnues.
- Compatibilité avec les navigateurs modernes.
