'use client'; // Nécessaire car utilise react-chartjs-2 et potentiellement des hooks internes

import React from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler, // Import Filler for filling area under the line
} from 'chart.js';
import annotationPlugin, { AnnotationOptions, LineAnnotationOptions } from 'chartjs-plugin-annotation'; // Import annotation plugin and types

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler, // Register Filler
  annotationPlugin // Register annotation plugin
);

interface EvolutionData {
  annee: number;
  total_patrimoine: number;
}

interface EvolutionChartProps {
  evolutionData: EvolutionData[];
  fireTargetAmount: number | null;
}

const EvolutionChart: React.FC<EvolutionChartProps> = ({ evolutionData, fireTargetAmount }) => {
  const data = {
    labels: evolutionData.map(d => d.annee.toString()),
    datasets: [
      {
        label: 'Patrimoine Total (€)',
        data: evolutionData.map(d => d.total_patrimoine),
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)', // Fill color
        tension: 0.1,
        fill: true, // Enable fill
      },
    ],
  };

  const annotationsConfig: Record<string, AnnotationOptions> = {};

  if (fireTargetAmount !== null) {
    const lineAnnotation: LineAnnotationOptions & { type: 'line' } = { // Assurer que 'type' est inclus
      type: 'line', // Explicitement ajouter 'type'
      yMin: fireTargetAmount,
      yMax: fireTargetAmount,
      borderColor: 'rgb(255, 99, 132)',
      borderWidth: 2,
      borderDash: [6, 6],
      label: {
        content: `Objectif FIRE: ${fireTargetAmount.toLocaleString('fr-FR', { style: 'currency', currency: 'EUR' })}`,
        display: true, // Changed from enabled to display for chartjs-plugin-annotation v2+
        position: 'end',
        backgroundColor: 'rgba(255, 99, 132, 0.8)',
        font: {
          weight: 'bold',
        },
        color: 'white',
        padding: {
          x: 6,
          y: 3,
        },
        yAdjust: -10,
      }
    };
    annotationsConfig.line1 = lineAnnotation;
  }

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value: string | number) {
            if (typeof value === 'number') {
              return value.toLocaleString('fr-FR', { style: 'currency', currency: 'EUR' });
            }
            return value;
          }
        }
      }
    },
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Évolution du Patrimoine Total et Objectif FIRE',
        font: {
          size: 18,
        }
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.y !== null) {
              label += new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(context.parsed.y);
            }
            return label;
          }
        }
      },
      annotation: {
        annotations: annotationsConfig
      }
    },
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
  };

  return (
    <div style={{ height: '400px', marginTop: '20px', marginBottom: '20px' }}>
      <Line data={data} options={options as any} /> {/* Use 'as any' for options if type issues persist with plugins */}
    </div>
  );
};

export default EvolutionChart;
