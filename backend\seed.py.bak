import datetime
from sqlalchemy.orm import Session
from database import SessionLocal, engine
import models
import crud, schemas

def seed_data():
    db = SessionLocal()
    models.Base.metadata.drop_all(bind=engine)
    models.Base.metadata.create_all(bind=engine)

    # Clear existing data
    db.query(models.DepenseReelle).delete() # Already here, good
    db.query(models.BudgetCategory).delete()
    db.query(models.FireAllocationTarget).delete()
    db.query(models.Liability).delete()
    db.query(models.SCPI).delete()
    db.query(models.AssetCategory).delete() # Ensuring this is before Asset and Category
    db.query(models.Category).delete() # Ensuring this is before Asset
    db.query(models.Asset).delete()
    db.query(models.PatrimoineHistory).delete()
    db.query(models.PatrimoineEvolution).delete()
    db.query(models.FireSettings).delete()
    db.commit()

    # --- Ensure All Categories Exist ---
    categories_to_seed = [
        models.Category(name='Bourse'),
        models.Category(name='Crypto-Actifs'),
        models.Category(name='Fonds sécurisés'),
        models.Category(name='Immobilier'),
        models.Category(name='Liquidité'),
        models.Category(name='Prêts participatifs')
    ]
    for cat_model_instance in categories_to_seed:
        existing_category = db.query(models.Category).filter(models.Category.name == cat_model_instance.name).first()
        if not existing_category:
            db.add(cat_model_instance)
    db.commit() # Commit categories before linking


    # --- Assets ---
    assets_to_create = [
        schemas.AssetCreate(name='Livret A', value=11056.0, annual_interest=700.0, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name='Liquidité', value=11056.0)]),
        schemas.AssetCreate(name='Livret Grand Format', value=10.0, annual_interest=None, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name='Liquidité', value=10.0)]),
        schemas.AssetCreate(name='BforBank Livret', value=13.0, annual_interest=None, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name='Liquidité', value=13.0)]),
        schemas.AssetCreate(name='BforBank Assurance Vie', value=4034.0, annual_interest=49.0, notes='', update_date=datetime.date(2025, 7, 2), categories=[schemas.CategoryValue(name='Fonds sécurisés', value=4034.0)]),
        schemas.AssetCreate(name='Linxea Avenir 2', value=3834.0, annual_interest=None, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name='Fonds sécurisés', value=3834.0)]),
        schemas.AssetCreate(name='October', value=138.0, annual_interest=None, notes='A stopper', update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name='Prêts participatifs', value=138.0)]),
        schemas.AssetCreate(name='Prexem', value=59.0, annual_interest=None, notes='A stopper', update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name='Prêts participatifs', value=59.0)]),
        schemas.AssetCreate(name='Mintos', value=14008.0, annual_interest=None, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name='Prêts participatifs', value=14008.0)]),
        schemas.AssetCreate(name='Lendosphere', value=1105.0, annual_interest=None, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name='Prêts participatifs', value=1105.0)]),
        schemas.AssetCreate(name='Trading 212', value=26336.0, annual_interest=None, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name='Bourse', value=26336.0)]),
        schemas.AssetCreate(name='BOURSE DIRECT PEA', value=85652.0, annual_interest=None, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name='Bourse', value=85652.0)]),
        schemas.AssetCreate(name='BOURSE DIRECT PME', value=2702.0, annual_interest=None, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name='Bourse', value=2702.0)]),
        schemas.AssetCreate(name='Trade Republic Bourse', value=22016.0, annual_interest=None, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name='Bourse', value=1120.0), schemas.CategoryValue(name='Fonds sécurisés', value=20896.0)]),
        schemas.AssetCreate(name='Bitpanda Crypto', value=22321.0, annual_interest=None, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name='Crypto-Actifs', value=22321.0)]),
        schemas.AssetCreate(name='Finst', value=17670.0, annual_interest=None, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name='Crypto-Actifs', value=17670.0)]),
        schemas.AssetCreate(name='Metamask', value=636.0, annual_interest=None, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name='Crypto-Actifs', value=636.0)]),
        schemas.AssetCreate(name='PER Linxea', value=27983.0, annual_interest=None, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name='Fonds sécurisés', value=27983.0)]),
        schemas.AssetCreate(name='Anaxago Immobilier', value=6610.0, annual_interest=849.0, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name='Immobilier', value=6610.0)]),
        schemas.AssetCreate(name='RealtT', value=1413.0, annual_interest=None, notes=None, update_date=datetime.date(2025, 7, 1), categories=[schemas.CategoryValue(name='Immobilier', value=1413.0)])
    ]

    # --- Assets (Generated Script Logic) ---
    # This assumes assets_to_create is a list of objects that have .name, .value, .categories etc.
    # (i.e. format_assets produces strings that result in a list of schemas.AssetCreate instances)
    for asset_data_item in assets_to_create:
        asset_db = models.Asset(
            name=asset_data_item.name,
            value=asset_data_item.value,
            annual_interest=asset_data_item.annual_interest,
            notes=asset_data_item.notes,
            update_date=asset_data_item.update_date
        )
        db.add(asset_db)
        db.flush() # Get asset_db.id

        for category_value_item in asset_data_item.categories:
            category_db_instance = db.query(models.Category).filter(models.Category.name == category_value_item.name).first()
            if category_db_instance: # Should exist due to pre-seeding
                asset_category_link = models.AssetCategory(
                    asset_id=asset_db.id,
                    category_id=category_db_instance.id,
                    value=category_value_item.value
                )
                db.add(asset_category_link)
            else:
                print(f"WARNING: Category '{category_value_item.name}' not found for asset '{asset_data_item.name}' during generated seed.")
    db.commit() # Commit assets and their category links

    # --- SCPI ---
    scpi_to_create = [
{
        "name": "EPARGNE FONCIERE",
        "price_per_share": 619.75,
        "prix_part_vente": 619.75,
        "number_of_shares": 38,
        "total_value": 23550.5,
        "update_date": datetime.date(2025, 7, 1),
        "scpi_lab_id": 39,
        "dividende_trimestriel": 285.0,
        "type_scpi": "CAPITAL VARIABLE",
        "societe_gestion": "La Française REM",
        "tdvm": 4.5,
        "taux_occupation": 92.3
},
{
        "name": "PFO2",
        "price_per_share": 150.06,
        "prix_part_vente": 150.06,
        "number_of_shares": 150,
        "total_value": 22509.0,
        "update_date": datetime.date(2025, 7, 1),
        "scpi_lab_id": 42,
        "dividende_trimestriel": 285.0,
        "type_scpi": "CAPITAL VARIABLE",
        "societe_gestion": "PERIAL Asset Management",
        "tdvm": 4.8,
        "taux_occupation": 93.1
},
{
        "name": "AESTIAM PIERRE RENDEMENT",
        "price_per_share": 829.8,
        "prix_part_vente": 829.8,
        "number_of_shares": 30,
        "total_value": 24894.0,
        "update_date": datetime.date(2025, 7, 1),
        "scpi_lab_id": 27,
        "dividende_trimestriel": 300.6,
        "type_scpi": "CAPITAL VARIABLE",
        "societe_gestion": "AESTIAM",
        "tdvm": 4.9,
        "taux_occupation": 91.8
},
{
        "name": "LF OPPORTUNITE IMMO",
        "price_per_share": 184.73,
        "prix_part_vente": 184.73,
        "number_of_shares": 57,
        "total_value": 10529.61,
        "update_date": datetime.date(2025, 7, 1),
        "scpi_lab_id": 56,
        "dividende_trimestriel": 162.45,
        "type_scpi": "CAPITAL VARIABLE",
        "societe_gestion": "La Française REM",
        "tdvm": 5.2,
        "taux_occupation": 94.5
}
    ]

    for scpi_data in scpi_to_create:
        scpi = models.SCPI(**scpi_data)
        db.add(scpi)

    # --- Liabilities ---
    liabilities_to_create = [
{
        "name": "Emprunt immobilier",
        "initial_amount": 60000.0,
        "remaining_capital": 49592.0,
        "interest_rate": 2.2,
        "end_date": datetime.date(2035, 11, 5),
        "monthly_payment": 465.85,
        "asset_id": None
}
    ]

    for liability_data in liabilities_to_create:
        liability = models.Liability(**liability_data)
        db.add(liability)

    # --- Patrimoine History ---
    patrimoine_history_to_create = [
        schemas.PatrimoineHistoryCreate(date=datetime.date(2024, 1, 1), net_patrimoine=150000.0),
        schemas.PatrimoineHistoryCreate(date=datetime.date(2024, 7, 1), net_patrimoine=160000.0),
        schemas.PatrimoineHistoryCreate(date=datetime.date(2025, 1, 1), net_patrimoine=170000.0),
        schemas.PatrimoineHistoryCreate(date=datetime.date(2025, 7, 1), net_patrimoine=180000.0)
    ]

    for history_data in patrimoine_history_to_create:
        history = models.PatrimoineHistory(**history_data.model_dump())
        db.add(history)

    # --- Patrimoine Evolution (2015-2024) ---
    evolution_to_create = [
{
        "annee": 2015,
        "investissement": 0.0,
        "prix_part_scpi": 0.0,
        "remboursement_credit": 0.0,
        "valeur_reelle_scpi": 0.0,
        "total_patrimoine": 81928.0,
        "evolution_pourcentage": None,
        "evolution_euros": None,
        "croissance_moyenne": None,
        "tcam": None
},
{
        "annee": 2016,
        "investissement": 0.0,
        "prix_part_scpi": 0.0,
        "remboursement_credit": 0.0,
        "valeur_reelle_scpi": 0.0,
        "total_patrimoine": 92466.0,
        "evolution_pourcentage": 12.86,
        "evolution_euros": 10538.0,
        "croissance_moyenne": 10538.0,
        "tcam": 12.86
},
{
        "annee": 2017,
        "investissement": 0.0,
        "prix_part_scpi": 0.0,
        "remboursement_credit": 0.0,
        "valeur_reelle_scpi": 0.0,
        "total_patrimoine": 95938.0,
        "evolution_pourcentage": 3.75,
        "evolution_euros": 3471.0,
        "croissance_moyenne": 7005.0,
        "tcam": 8.21
},
{
        "annee": 2018,
        "investissement": 0.0,
        "prix_part_scpi": 0.0,
        "remboursement_credit": 0.0,
        "valeur_reelle_scpi": 0.0,
        "total_patrimoine": 101860.0,
        "evolution_pourcentage": 6.17,
        "evolution_euros": 5922.0,
        "croissance_moyenne": 6644.0,
        "tcam": 7.53
},
{
        "annee": 2019,
        "investissement": 0.0,
        "prix_part_scpi": 0.0,
        "remboursement_credit": 0.0,
        "valeur_reelle_scpi": 0.0,
        "total_patrimoine": 112117.0,
        "evolution_pourcentage": 10.07,
        "evolution_euros": 10257.0,
        "croissance_moyenne": 7547.0,
        "tcam": 8.16
},
{
        "annee": 2020,
        "investissement": 0.0,
        "prix_part_scpi": 0.0,
        "remboursement_credit": 0.0,
        "valeur_reelle_scpi": 0.0,
        "total_patrimoine": 119727.0,
        "evolution_pourcentage": 6.79,
        "evolution_euros": 7609.0,
        "croissance_moyenne": 7560.0,
        "tcam": 7.88
},
{
        "annee": 2021,
        "investissement": 0.0,
        "prix_part_scpi": 0.0,
        "remboursement_credit": 0.0,
        "valeur_reelle_scpi": 0.0,
        "total_patrimoine": 147610.0,
        "evolution_pourcentage": 23.29,
        "evolution_euros": 27883.0,
        "croissance_moyenne": 10947.0,
        "tcam": 10.31
},
{
        "annee": 2022,
        "investissement": 0.0,
        "prix_part_scpi": 0.0,
        "remboursement_credit": 0.0,
        "valeur_reelle_scpi": 0.0,
        "total_patrimoine": 154534.0,
        "evolution_pourcentage": 4.69,
        "evolution_euros": 6924.0,
        "croissance_moyenne": 10372.0,
        "tcam": 9.49
},
{
        "annee": 2023,
        "investissement": 0.0,
        "prix_part_scpi": 0.0,
        "remboursement_credit": 0.0,
        "valeur_reelle_scpi": 0.0,
        "total_patrimoine": 194626.0,
        "evolution_pourcentage": 30.46,
        "evolution_euros": 40092.0,
        "croissance_moyenne": 14087.0,
        "tcam": 11.42
},
{
        "annee": 2024,
        "investissement": 0.0,
        "prix_part_scpi": 0.0,
        "remboursement_credit": 0.0,
        "valeur_reelle_scpi": 0.0,
        "total_patrimoine": 257466.0,
        "evolution_pourcentage": 37.01,
        "evolution_euros": 62841.0,
        "croissance_moyenne": 19504.0,
        "tcam": 13.57
}
    ]

    for evolution_data in evolution_to_create:
        evolution = models.PatrimoineEvolution(**evolution_data)
        db.add(evolution)

    # --- Budget Categories (25 catégories FIRE) ---
    budget_categories_to_create = [
{
        "nom": "Alimentation",
        "budget_annuel": 4860.0,
        "description": "Courses alimentaires",
        "ordre_affichage": 1
},
{
        "nom": "Assurance Maison",
        "budget_annuel": 956.0,
        "description": "Assurance habitation",
        "ordre_affichage": 2
},
{
        "nom": "Internet",
        "budget_annuel": 480.0,
        "description": "Abonnement internet",
        "ordre_affichage": 3
},
{
        "nom": "Mobile forfait",
        "budget_annuel": 180.0,
        "description": "Forfait téléphone mobile",
        "ordre_affichage": 4
},
{
        "nom": "Frais Banque",
        "budget_annuel": 162.0,
        "description": "Frais bancaires",
        "ordre_affichage": 5
},
{
        "nom": "Ramonage",
        "budget_annuel": 74.0,
        "description": "Entretien cheminée",
        "ordre_affichage": 6
},
{
        "nom": "Assurance Voiture",
        "budget_annuel": 473.0,
        "description": "Assurance automobile",
        "ordre_affichage": 7
},
{
        "nom": "Taxe Foncière",
        "budget_annuel": 770.0,
        "description": "Impôts locaux",
        "ordre_affichage": 8
},
{
        "nom": "Eau",
        "budget_annuel": 189.0,
        "description": "Facture d'eau",
        "ordre_affichage": 9
},
{
        "nom": "Poubelle",
        "budget_annuel": 192.0,
        "description": "Taxe ordures ménagères",
        "ordre_affichage": 10
},
{
        "nom": "Électricité",
        "budget_annuel": 1800.0,
        "description": "Facture électricité",
        "ordre_affichage": 11
},
{
        "nom": "Carburant (BioEthanol)",
        "budget_annuel": 1944.0,
        "description": "Essence bioéthanol",
        "ordre_affichage": 12
},
{
        "nom": "Bois de chauffage",
        "budget_annuel": 2025.0,
        "description": "Chauffage au bois",
        "ordre_affichage": 13
},
{
        "nom": "Voiture entretien",
        "budget_annuel": 405.0,
        "description": "Entretien véhicule",
        "ordre_affichage": 14
},
{
        "nom": "Pneus Voiture (provision /4 ans)",
        "budget_annuel": 270.0,
        "description": "Provision pneus",
        "ordre_affichage": 15
},
{
        "nom": "Divers (100€/mois)",
        "budget_annuel": 1200.0,
        "description": "Dépenses diverses",
        "ordre_affichage": 16
},
{
        "nom": "Mutuelle",
        "budget_annuel": 2430.0,
        "description": "Complémentaire santé",
        "ordre_affichage": 17
},
{
        "nom": "Cotisation Subsidiaire Maladie (CSM)",
        "budget_annuel": 1300.0,
        "description": "CSM",
        "ordre_affichage": 18
},
{
        "nom": "Santé (Reste à charge / non remboursé)",
        "budget_annuel": 300.0,
        "description": "Frais santé",
        "ordre_affichage": 19
},
{
        "nom": "Vacances",
        "budget_annuel": 1080.0,
        "description": "Budget vacances",
        "ordre_affichage": 20
},
{
        "nom": "Streaming",
        "budget_annuel": 142.0,
        "description": "Abonnements streaming",
        "ordre_affichage": 21
},
{
        "nom": "PC (provision /8 ans)",
        "budget_annuel": 313.0,
        "description": "Provision ordinateur",
        "ordre_affichage": 22
},
{
        "nom": "Smartphone (provision /6 ans)",
        "budget_annuel": 142.0,
        "description": "Provision téléphone",
        "ordre_affichage": 23
},
{
        "nom": "Voiture (provision /15 ans, base 25k€)",
        "budget_annuel": 1667.0,
        "description": "Provision véhicule",
        "ordre_affichage": 24
},
{
        "nom": "Coûts d'entretien maison (base 125k€ * 2%)",
        "budget_annuel": 2130.0,
        "description": "Entretien maison",
        "ordre_affichage": 25
}
    ]

    for category_data in budget_categories_to_create:
        category = models.BudgetCategory(**category_data)
        db.add(category)

    # --- Dépenses Réelles ---


    # --- Fire Settings ---
    fire_setting_data = models.FireSettings(
        fire_target_amount=910150.0,
        secure_withdrawal_rate=0.04,
        update_date=datetime.date(2025, 7, 4)
    )
    db.add(fire_setting_data)

    # --- Fire Allocation Targets ---
    fire_allocation_targets_to_create = [
        models.FireAllocationTarget(category_key='Liquidité', target_percentage=2.5),
        models.FireAllocationTarget(category_key='Bourse', target_percentage=45.5),
        models.FireAllocationTarget(category_key='Immobilier', target_percentage=25.0),
        models.FireAllocationTarget(category_key='Fonds sécurisés', target_percentage=20.0),
        models.FireAllocationTarget(category_key='Prêts participatifs', target_percentage=2.0),
        models.FireAllocationTarget(category_key='Crypto-Actifs', target_percentage=5.0)
    ]

    for fat_data in fire_allocation_targets_to_create:
        db.add(fat_data)

    db.commit()

    # Calculate and display totals
    total_scpi_value = sum(scpi_data["total_value"] for scpi_data in scpi_to_create)
    total_liabilities = sum(liability_data["remaining_capital"] for liability_data in liabilities_to_create)
    total_budget_annuel = sum(cat["budget_annuel"] for cat in budget_categories_to_create)

    print("Database has been seeded successfully.")
    print(f"Assets created: {len(assets_to_create)}")
    print(f"SCPI created: {len(scpi_to_create)}")
    print(f"Total SCPI value: {total_scpi_value:,.2f} €")
    print(f"Liabilities created: {len(liabilities_to_create)}")
    print(f"Total liabilities: {total_liabilities:,.2f} €")
    print(f"Patrimoine history entries: {len(patrimoine_history_to_create)}")
    print(f"Evolution entries: {len(evolution_to_create)} (2015-2024)")
    print(f"Budget categories: {len(budget_categories_to_create)}")
    print(f"Total budget annuel FIRE: {total_budget_annuel:,.2f} €")
    print(f"Retrait brut nécessaire (avec 30% impôts/PS): {total_budget_annuel * 1.3:,.2f} €")
    print("Evolution metrics calculated automatically.")

    db.close()
    engine.dispose()

if __name__ == "__main__":
    seed_data()
