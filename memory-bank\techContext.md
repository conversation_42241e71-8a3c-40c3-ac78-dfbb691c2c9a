# Contexte Technique - Fire UI

## Technologies utilisées
- **Frontend Principal (`frontnextjs/`)** - ✅ **ACTIF** :
  - Next.js (v15.3.5) utilisant React (v19.1.0)
  - TypeScript (v5)
  - App Router pour le routage moderne
  - Tailwind CSS (v3.4.0) pour les utilitaires CSS
  - Bootstrap 5 (v5.3.7) pour l'interface utilisateur
  - Chart.js (v4.5.0) et react-chartjs-2 (v5.3.0) pour les graphiques
  - Axios (v1.10.0) pour les appels API
  - Jest (v29.7.0) pour les tests unitaires
  - ESLint (v9) et TypeScript pour la qualité du code
- **Frontend Archivé (`frontend/`)** - 📦 **RÉFÉRENCE** :
  - React.js (v19) avec TypeScript (Create React App)
  - Bootstrap 5, Chart.js, Axios
- **Backend (`backend/`)** :
  - Python avec **FastAPI**
  - SQLAlchemy pour l'ORM
  - SQLite comme base de données

## Configuration du développement
- **Frontend Principal (`frontnextjs/`)** :
  - Initialisé avec `create-next-app` et migré depuis React CRA
  - `package.json` pour la gestion des dépendances
  - `next.config.ts` avec configuration pour ignorer les erreurs de build temporairement
  - `tailwind.config.js` pour la configuration Tailwind CSS v3
  - `postcss.config.js` pour le traitement CSS
  - `jest.config.js` pour la configuration des tests
  - `eslint.config.mjs` avec règles adaptées pour NextJS
  - `tsconfig.json` pour TypeScript
- **Frontend Archivé (`frontend/`)** :
  - Basé sur Create React App (conservé pour référence)
- **Backend (`backend/`)** :
  - Virtualenv pour gérer les dépendances Python
  - `requirements.txt` pour lister les bibliothèques nécessaires

## Contraintes techniques
- ✅ Migration terminée : Compatibilité assurée entre le frontend Next.js et le backend FastAPI
- Assurer la sécurité des données financières
- Maintenir une bonne expérience utilisateur (objectif atteint)
- Gestion des erreurs TypeScript et ESLint (configuration adaptée)

## Dépendances Clés
- **Frontend Principal (`frontnextjs/`)** :
  - **Core** : `next@15.3.5`, `react@19.1.0`, `react-dom@19.1.0`
  - **UI** : `bootstrap@5.3.7`, `react-bootstrap@2.10.10`
  - **Styling** : `tailwindcss@3.4.0`, `postcss`, `autoprefixer`
  - **Charts** : `chart.js@4.5.0`, `react-chartjs-2@5.3.0`, `chartjs-plugin-annotation@3.1.0`
  - **HTTP** : `axios@1.10.0`
  - **Testing** : `jest@29.7.0`, `jest-environment-jsdom@29.7.0`, `@testing-library/react@16.3.0`, `@testing-library/jest-dom@6.6.3`
  - **Dev Tools** : `typescript@5`, `eslint@9`, `eslint-config-next@15.3.5`
  - **Bootstrap JS** : Chargé dynamiquement via `BootstrapClient.tsx`
- **Frontend Archivé (`frontend/`)** :
  - `react`, `react-dom`, `react-scripts` (Create React App)
  - `axios`, `bootstrap`, `chart.js`, `react-bootstrap`, `react-chartjs-2`
- **Backend (`backend/`)** :
  - `fastapi`, `uvicorn`, `sqlalchemy`, `pydantic`
  - `python-jose[cryptography]` pour la sécurité (si authentification JWT est ajoutée)
  - `requests` et `BeautifulSoup4` pour le web scraping des données SCPI

## Configuration des outils
- **Frontend Principal (`frontnextjs/`)** :
  - ✅ ESLint configuré avec règles adaptées (erreurs converties en warnings)
  - ✅ TypeScript configuré avec `ignoreBuildErrors: true` temporairement
  - ✅ Tests unitaires avec Jest et Testing Library (configuration complète)
  - ✅ Tailwind CSS v3 configuré avec PostCSS
  - ✅ Build de production fonctionnel
- **Backend (`backend/`)** :
  - Pas de linter spécifique, mais des outils comme Black ou Flake8 pourraient être ajoutés

## État de la Migration
- ✅ **Migration Terminée** : Toutes les fonctionnalités ont été migrées avec succès
- ✅ **Build Fonctionnel** : L'application compile et démarre sans erreur
- ✅ **Tests Configurés** : Jest et Testing Library opérationnels
- ✅ **Styling Intégré** : Bootstrap 5 + Tailwind CSS v3 fonctionnent ensemble
- ✅ **API Connectée** : Communication avec le backend FastAPI opérationnelle
- ✅ **Interface Utilisateur** : Navigation et arrière-plan corrigés (Décembre 2024)
- ⚠️ **Optimisations** : Quelques warnings TypeScript/ESLint à corriger (non bloquants)

## Corrections Post-Migration (Décembre 2024)
### Problèmes d'Interface Résolus
- **Navigation manquante** : Restructuration complète de la navbar Bootstrap
  - Ajout de `BootstrapClient.tsx` pour charger Bootstrap JavaScript
  - CSS responsive avec `d-lg-flex` pour affichage desktop
  - Menu mobile séparé avec gestion d'état React
- **Arrière-plan noir** : Configuration CSS corrigée
  - Variables CSS forcées en mode clair : `--background: #ffffff`
  - Désactivation du mode sombre automatique
  - Styles `!important` pour priorité CSS
