#!/usr/bin/env python3
"""
Script pour corriger les données SCPI dans la base de données
selon les valeurs de référence fournies
"""

import sqlite3
from datetime import datetime

# Données de référence correctes
REFERENCE_DATA = [
    {
        "name": "EPARGNE FONCIERE",
        "price_per_share": 619.75,
        "number_of_shares": 38,
        "total_value": 23550.50,
        "prix_part_vente": 619.75,
        "dividende_trimestriel": 7.50
    },
    {
        "name": "PFO2", 
        "price_per_share": 150.06,
        "number_of_shares": 150,
        "total_value": 22509.00,
        "prix_part_vente": 150.06,
        "dividende_trimestriel": 1.90
    },
    {
        "name": "AESTIAM PIERRE RENDEMENT",
        "price_per_share": 829.80,
        "number_of_shares": 30,
        "total_value": 24894.00,
        "prix_part_vente": 829.80,
        "dividende_trimestriel": 10.02
    },
    {
        "name": "LF OPPORTUNITE IMMO",
        "price_per_share": 184.73,
        "number_of_shares": 57,
        "total_value": 10529.61,
        "prix_part_vente": 184.73,
        "dividende_trimestriel": 2.85
    }
]

def connect_db():
    """Connexion à la base de données"""
    return sqlite3.connect('patrimoine.db')

def get_current_data():
    """Récupère les données actuelles"""
    conn = connect_db()
    cursor = conn.cursor()
    
    cursor.execute("""
        SELECT id, name, price_per_share, number_of_shares, total_value, 
               prix_part_vente, dividende_trimestriel
        FROM scpi
        ORDER BY id
    """)
    
    current_data = cursor.fetchall()
    conn.close()
    
    return current_data

def update_scpi_data():
    """Met à jour les données SCPI avec les valeurs de référence"""
    conn = connect_db()
    cursor = conn.cursor()
    
    print("🔧 Correction des données SCPI...")
    print("=" * 60)
    
    for ref_data in REFERENCE_DATA:
        # Recherche de la SCPI par nom
        cursor.execute("SELECT id FROM scpi WHERE name = ?", (ref_data["name"],))
        result = cursor.fetchone()
        
        if result:
            scpi_id = result[0]
            
            # Mise à jour des données
            cursor.execute("""
                UPDATE scpi 
                SET price_per_share = ?,
                    number_of_shares = ?,
                    total_value = ?,
                    prix_part_vente = ?,
                    dividende_trimestriel = ?,
                    update_date = ?
                WHERE id = ?
            """, (
                ref_data["price_per_share"],
                ref_data["number_of_shares"], 
                ref_data["total_value"],
                ref_data["prix_part_vente"],
                ref_data["dividende_trimestriel"],
                datetime.now().strftime("%Y-%m-%d"),
                scpi_id
            ))
            
            print(f"✅ {ref_data['name']} - Données mises à jour")
            print(f"   Prix: {ref_data['price_per_share']}€")
            print(f"   Parts: {ref_data['number_of_shares']}")
            print(f"   Total: {ref_data['total_value']}€")
            print(f"   Dividende: {ref_data['dividende_trimestriel']}€/part")
            print()
        else:
            print(f"❌ SCPI '{ref_data['name']}' non trouvée dans la base")
    
    conn.commit()
    conn.close()
    
    print("✅ Correction terminée !")

def verify_data():
    """Vérifie les données après correction"""
    print("\n🔍 Vérification des données corrigées...")
    print("=" * 60)
    
    current_data = get_current_data()
    
    total_value = 0
    total_dividends = 0
    total_shares = 0
    
    for row in current_data:
        id, name, price, shares, value, prix_vente, dividende = row
        total_value += value
        total_dividends += dividende * shares
        total_shares += shares
        
        print(f"📊 {name}")
        print(f"   Prix: {price}€ | Parts: {shares} | Total: {value}€")
        print(f"   Dividende/part: {dividende}€ | Total dividendes: {dividende * shares}€")
        print()
    
    print("📈 TOTAUX DU PORTEFEUILLE:")
    print(f"   Valeur totale: {total_value:,.2f}€")
    print(f"   Nombre total de parts: {total_shares}")
    print(f"   Dividendes trimestriels totaux: {total_dividends:,.2f}€")
    
    # Vérification avec les valeurs de référence
    expected_total = 81483.11
    expected_dividends = 1033.05
    
    print(f"\n🎯 COMPARAISON AVEC LES VALEURS ATTENDUES:")
    print(f"   Valeur totale: {total_value:,.2f}€ (attendu: {expected_total:,.2f}€)")
    print(f"   Dividendes: {total_dividends:,.2f}€ (attendu: {expected_dividends:,.2f}€)")
    
    if abs(total_value - expected_total) < 1:
        print("   ✅ Valeur totale correcte")
    else:
        print("   ❌ Écart sur la valeur totale")
        
    if abs(total_dividends - expected_dividends) < 1:
        print("   ✅ Dividendes corrects")
    else:
        print("   ❌ Écart sur les dividendes")

def main():
    """Fonction principale"""
    print("🚀 Script de correction des données SCPI")
    print("=" * 60)
    
    # Affichage des données actuelles
    print("📋 Données actuelles:")
    current_data = get_current_data()
    for row in current_data:
        id, name, price, shares, value, prix_vente, dividende = row
        print(f"   {name}: {price}€ × {shares} = {value}€ (div: {dividende}€/part)")
    
    print()
    
    # Correction des données
    update_scpi_data()
    
    # Vérification
    verify_data()

if __name__ == "__main__":
    main()
