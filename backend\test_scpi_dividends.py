#!/usr/bin/env python3
"""
Script de test pour vérifier l'extraction des données de dividendes SCPI
"""

import sys
import os
from pathlib import Path

# Ajouter le répertoire backend au path pour les imports
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from scpi_scraper import SCPIScraper, get_scpi_data

def test_utility_methods():
    """Test des méthodes utilitaires d'extraction"""
    print("🧪 Test des méthodes utilitaires...")
    
    scraper = SCPIScraper(headless=True)
    
    # Test extract_currency_amount
    test_cases_currency = [
        ("1,33 M€", 1.33),
        ("2.5 M€", 2.5),
        ("750 K€", 0.75),
        ("1500000 €", 1.5),
        ("- M€", None),
        ("", None)
    ]
    
    print("\n📊 Test extract_currency_amount:")
    for text, expected in test_cases_currency:
        result = scraper.extract_currency_amount(text)
        status = "✅" if result == expected else "❌"
        print(f"  {status} '{text}' -> {result} (attendu: {expected})")
    
    # Test extract_dividend_per_share
    test_cases_dividend = [
        ("7.5€/part", 7.5),
        ("8,25 €/part", 8.25),
        ("6.75 euros/part", 6.75),
        ("9,50 € par part", 9.5),
        ("- €/part", None),
        ("", None)
    ]
    
    print("\n💰 Test extract_dividend_per_share:")
    for text, expected in test_cases_dividend:
        result = scraper.extract_dividend_per_share(text)
        status = "✅" if result == expected else "❌"
        print(f"  {status} '{text}' -> {result} (attendu: {expected})")
    
    # Test extract_trimester
    test_cases_trimester = [
        ("T1-2025", "T1-2025"),
        ("T2 2024", "T2-2024"),
        ("1er trimestre 2025", "T1-2025"),
        ("Q4-2024", "T4-2024"),
        ("", None)
    ]
    
    print("\n📅 Test extract_trimester:")
    for text, expected in test_cases_trimester:
        result = scraper.extract_trimester(text)
        status = "✅" if result == expected else "❌"
        print(f"  {status} '{text}' -> {result} (attendu: {expected})")
    
    scraper.close()
    print("\n✅ Tests des méthodes utilitaires terminés")

def test_scpi_extraction(scpi_id="1"):
    """Test de l'extraction complète d'une SCPI"""
    print(f"\n🔍 Test d'extraction pour SCPI ID: {scpi_id}")
    
    try:
        # Test avec le scraper avancé
        scraper = SCPIScraper(headless=True)
        scpi_data = scraper.scrape_scpi(scpi_id)
        
        print(f"\n📋 Résultats d'extraction pour {scpi_data.general_info.nom}:")
        print(f"  🏢 Société de gestion: {scpi_data.general_info.societe_gestion}")
        print(f"  💰 Prix actuel: {scpi_data.chiffres_cles.prix_part_actuel}€")
        
        # Focus sur les données trimestrielles
        trimestre_info = scpi_data.trimestre_info
        print(f"\n📊 Données trimestrielles ({trimestre_info.trimestre}):")
        print(f"  💵 Collecte brute: {trimestre_info.collecte_brute}")
        print(f"  🎯 Acompte distribué: {trimestre_info.acompte_brut}€/part")
        print(f"  🔄 Nombre de cessions: {trimestre_info.nb_cessions}")
        print(f"  📈 Montant des cessions: {trimestre_info.montant_cessions}")
        
        scraper.close()
        
        # Test avec l'API de compatibilité
        print(f"\n🔄 Test de l'API de compatibilité...")
        api_data = get_scpi_data(scpi_id)
        
        print(f"📋 Données API pour {api_data.nom}:")
        print(f"  📅 Trimestre courant: {api_data.trimestre_courant}")
        print(f"  💵 Collecte brute 2024: {api_data.collecte_brute_2024}")
        print(f"  🎯 Acompte dernier trimestre: {api_data.acompte_dernier_trimestre}€/part")
        print(f"  🏢 Société de gestion: {api_data.societe_gestion}")
        print(f"  📊 Taux de distribution: {api_data.taux_distribution}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test d'extraction: {e}")
        return False

def test_dividend_data_structure():
    """Test de la structure des données de dividendes"""
    print("\n🏗️ Test de la structure des données de dividendes...")
    
    # Vérifier que les champs requis sont présents
    from scpi_scraper import SCPITrimestreInfo, ScpiData
    
    # Test SCPITrimestreInfo
    required_fields_trimestre = [
        'trimestre', 'collecte_brute', 'acompte_brut', 'nb_cessions'
    ]
    
    print("📊 Vérification SCPITrimestreInfo:")
    for field in required_fields_trimestre:
        if hasattr(SCPITrimestreInfo, '__annotations__') and field in SCPITrimestreInfo.__annotations__:
            print(f"  ✅ {field}: {SCPITrimestreInfo.__annotations__[field]}")
        else:
            print(f"  ❌ {field}: MANQUANT")
    
    # Test ScpiData (API compatibility)
    required_fields_api = [
        'trimestre_courant', 'collecte_brute_2024', 'acompte_dernier_trimestre'
    ]
    
    print("\n📋 Vérification ScpiData (API):")
    for field in required_fields_api:
        if hasattr(ScpiData, '__annotations__') and field in ScpiData.__annotations__:
            print(f"  ✅ {field}: {ScpiData.__annotations__[field]}")
        else:
            print(f"  ❌ {field}: MANQUANT")
    
    print("\n✅ Vérification de la structure terminée")

def main():
    """Fonction principale de test"""
    print("🚀 Démarrage des tests d'extraction des dividendes SCPI")
    print("=" * 60)
    
    # Test 1: Méthodes utilitaires
    test_utility_methods()
    
    # Test 2: Structure des données
    test_dividend_data_structure()
    
    # Test 3: Extraction réelle (optionnel - nécessite une connexion internet)
    print("\n" + "=" * 60)
    response = input("🌐 Voulez-vous tester l'extraction réelle (nécessite internet) ? (y/N): ")
    
    if response.lower() in ['y', 'yes', 'oui']:
        scpi_id = input("📝 Entrez l'ID SCPI à tester (défaut: 1): ").strip() or "1"
        success = test_scpi_extraction(scpi_id)
        
        if success:
            print("\n🎉 Test d'extraction réelle réussi !")
        else:
            print("\n💥 Test d'extraction réelle échoué !")
    else:
        print("\n⏭️ Test d'extraction réelle ignoré")
    
    print("\n" + "=" * 60)
    print("✅ Tous les tests terminés !")

if __name__ == "__main__":
    main()
