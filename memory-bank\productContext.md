# Contexte Produit - Fire UI

## Problème à résoudre
De nombreux individus cherchent à atteindre la liberté financière (FIRE) mais manquent des outils pour planifier et visualiser leur progression. Actuellement, il n'existe pas de solution intégrée qui permet aux utilisateurs d'entrer leurs données financières, de simuler différents scénarios et de visualiser l'évolution de leur patrimoine dans le temps.

## Solution proposée
Fire UI est une application web qui offre une interface intuitive pour entrer les actifs, passifs et revenus des utilisateurs. Elle permet également de simuler divers scénarios financiers et de visualiser l'évolution du patrimoine au fil des ans. L'application aide les utilisateurs à mieux comprendre leur situation financière actuelle et à planifier leur avenir financier.

## Objectifs utilisateur
- Faciliter la saisie des données financières personnelles
- Offrir une visualisation claire et intuitive des scénarios financiers
- Aider les utilisateurs à atteindre leurs objectifs de liberté financière

## Expérience utilisateur cible
L'application doit être accessible, facile à utiliser et fournir des informations claires et précises. Les utilisateurs doivent pouvoir entrer leurs données financières rapidement et voir immédiatement l'impact de ces données sur leur situation financière future.

## Fonctionnalités clés
- Interface pour saisir les actifs, passifs et revenus
- Simulation des scénarios FIRE
- Visualisation des tendances annuelles du patrimoine, avec des métriques détaillées telles que le TCAM, l'évolution en valeur absolue et en pourcentage, ainsi que la croissance annuelle moyenne.
- Gestion détaillée des SCPI avec possibilité de récupérer des informations dynamiques via web scraping, suivi des dividendes trimestriels, prix de vente par part, et calcul automatique des totaux. Interface simplifiée n'affichant que les champs pertinents et mise à jour automatique des données via le bouton "MàJ Auto".
  - **Extraction robuste du prix de vente** : Système amélioré pour extraire correctement le prix de vente des parts SCPI (prix_part_vente) avec une approche plus fiable et une valeur par défaut, permettant une valorisation plus précise du patrimoine.
  - **Préservation des valeurs personnalisées** : Logique de mise à jour qui préserve les valeurs personnalisées de prix_part_vente lors des mises à jour automatiques.
