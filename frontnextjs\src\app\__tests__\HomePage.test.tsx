import React from 'react';
import { render, screen } from '@testing-library/react';
// Importer DashboardClientPart depuis le fichier page.tsx.
// Pour cela, il faut exporter DashboardClientPart depuis page.tsx ou le déplacer dans son propre fichier.
// Importer DashboardClientPart depuis le fichier page.tsx.
import { DashboardClientPart } from '../page'; // Ajuster le chemin si nécessaire
import apiClient from '../../components/ApiClient'; // Importé pour mocker

// Mocker react-chartjs-2 pour éviter les erreurs de rendu Canvas dans JSDOM
jest.mock('react-chartjs-2', () => ({
  Doughnut: () => <div data-testid="mock-doughnut-chart" />,
  // Ajoutez d'autres types de graphiques si nécessaire, ex: Line: () => <div data-testid="mock-line-chart" />
}));

// Mocker Chart.register de chart.js
// page.tsx appelle ChartJS.register(Arc<PERSON>lement, <PERSON><PERSON><PERSON>, Legend);
// ChartJS est importé depuis 'chart.js'
// Nous devons donc mocker le module 'chart.js' pour contrôler Chart.register.
jest.mock('chart.js', () => {
  const originalModule = jest.requireActual('chart.js');
  return {
    ...originalModule, // Exporter tout le reste du module original
    Chart: { // Mocker l'objet Chart
      ...originalModule.Chart, // Conserver les autres propriétés/méthodes de Chart
      register: jest.fn(), // Mocker la fonction statique register
    },
    // Si les éléments individuels (ArcElement, etc.) causent des problèmes à l'import,
    // ils peuvent aussi être mockés ici, mais ce n'est souvent pas nécessaire
    // si seul Chart.register est appelé.
    // ArcElement: jest.fn(),
    // Tooltip: jest.fn(),
    // Legend: jest.fn(),
  };
});

// Mocker apiClient pour éviter les vrais appels API dans les tests unitaires de ce composant
jest.mock('../../components/ApiClient', () => ({
  __esModule: true,
  default: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
  },
}));


describe('DashboardClientPart', () => {
  // Réinitialiser les mocks avant chaque test
  beforeEach(() => {
    (apiClient.post as jest.Mock).mockClear();
  });

  it('renders a heading and dashboard data when provided', () => {
    const mockDashboardData = {
      net_patrimoine: 100000,
      total_assets: 150000,
      total_liabilities: 50000,
      allocation: { 'Bourse': 75000, 'Immobilier': 75000 }, // S'assurer que les allocations existent
    };
    const mockFireTargetsData = [
      { id: 1, category_key: 'Bourse', target_percentage: 50 },
      { id: 2, category_key: 'Immobilier', target_percentage: 50 }
    ];

    render(
      <DashboardClientPart
        initialDashboardData={mockDashboardData}
        initialFireTargetsData={mockFireTargetsData}
      />
    );

    expect(screen.getByRole('heading', { name: /dashboard/i })).toBeInTheDocument();
    // Vérifier un des éléments qui affiche les données
    expect(screen.getByText(/Patrimoine Net Total/i)).toBeInTheDocument();
    // Utiliser une regex pour être flexible avec l'espace (normal vs. insécable)
    expect(screen.getByText(/100\s*000\s*€/)).toBeInTheDocument();
  });

  it('renders error message if fetchError is provided', () => {
    render(
      <DashboardClientPart
        initialDashboardData={null}
        initialFireTargetsData={null}
        fetchError="Erreur de test du serveur"
      />
    );
    expect(screen.getByText(/Erreur de test du serveur/i)).toBeInTheDocument();
  });

  it('renders no data message if initial data is null and no fetch error', () => {
    render(
      <DashboardClientPart
        initialDashboardData={null}
        initialFireTargetsData={null}
      />
    );
    // Le composant affiche "Données du dashboard non disponibles."
    expect(screen.getByText(/Données du dashboard non disponibles./i)).toBeInTheDocument();
  });

  // Test pour la fonctionnalité de sauvegarde (très simplifié)
  it('calls apiClient.post when save changes is clicked with changes', async () => {
    const mockDashboardData = {
      net_patrimoine: 100000, total_assets: 150000, total_liabilities: 50000,
      allocation: { 'Liquidité': 10000, 'Bourse': 140000 },
    };
    const mockFireTargetsData = [
      { id: 1, category_key: 'Liquidité', target_percentage: 5 },
      { id: 2, category_key: 'Bourse', target_percentage: 95 },
    ];

    // Simuler une résolution pour le post
    (apiClient.post as jest.Mock).mockResolvedValueOnce({ data: {} });

    // window.confirm doit être mocké pour les tests non interactifs
    jest.spyOn(window, 'confirm').mockImplementation(() => true);

    render(
      <DashboardClientPart
        initialDashboardData={mockDashboardData}
        initialFireTargetsData={mockFireTargetsData}
      />
    );

    // Simuler un changement de pourcentage cible
    // Ceci est complexe car il faut interagir avec les champs input.
    // Pour un test plus simple, on pourrait vérifier l'état initial du bouton.
    const saveButton = screen.getByRole('button', { name: /Sauvegarder les % Cibles/i });
    expect(saveButton).toBeDisabled(); // Devrait être désactivé initialement car pas de modifs

    // Ici, il faudrait simuler un changement dans un input pour activer le bouton.
    // Exemple:
    // const firstTargetInput = screen.getAllByRole('spinbutton')[0]; // Pas idéal, dépend de l'ordre
    // fireEvent.change(firstTargetInput, { target: { value: '10' } });
    // expect(saveButton).toBeEnabled();
    // fireEvent.click(saveButton);
    // await waitFor(() => expect(apiClient.post).toHaveBeenCalledTimes(1));
  });

});
