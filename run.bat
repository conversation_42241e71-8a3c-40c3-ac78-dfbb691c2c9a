@echo OFF
ECHO "Starting backend server..."
REM Assurez-vous que l'environnement virtuel est activé.
REM Le script install.bat devrait déjà l'avoir créé et activé pour l'installation.
REM Pour run.bat, il est préférable de s'assurer qu'il est activé à chaque fois si nécessaire,
REM ou que les commandes Python/uvicorn sont accessibles globalement ou via le .venv.

start "Backend" cmd /k "cd backend && .\.venv\Scripts\activate && uvicorn main:app --reload --port 8000"

ECHO "Starting Next.js frontend server (frontnextjs)..."
start "Frontend Next.js" cmd /k "cd frontnextjs && npm run dev"

ECHO.
ECHO ==================================================================
ECHO Serveurs en cours de démarrage...
ECHO.
ECHO    - Backend API: http://localhost:8000
ECHO    - Documentation API: http://localhost:8000/docs
ECHO    - Frontend (Next.js): http://localhost:3000
ECHO ==================================================================
ECHO.

