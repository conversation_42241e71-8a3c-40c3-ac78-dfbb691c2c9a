'use client'; // Ce composant utilise useState, useEffect, etc.

import React, { useEffect, useState, FormEvent } from 'react';
import apiClient from '../../components/ApiClient'; // Chemin ajusté
import { Modal, Button, Form, Tabs, Tab } from 'react-bootstrap';

interface SCPI {
  id: number;
  name: string;
  price_per_share: number;
  number_of_shares: number;
  total_value: number;
  update_date: string;
  scpi_lab_id?: number;
  type_scpi?: string;
  societe_gestion?: string;
  tdvm?: number;
  taux_occupation?: number;
  dividende_trimestriel?: number;
  prix_part_vente?: number;
}

interface ScrapedScpiData {
  // Informations générales
  nom?: string;
  type_scpi?: string;
  societe_gestion?: string;
  statut?: string;
  type_capital?: string;
  type_actifs?: string;
  localisation_principale?: string;
  annee_creation?: number;
  
  // Performance et valorisation
  tdvm?: string;
  taux_distribution_brut?: number;
  taux_distribution_net?: number;
  taux_occupation?: string;
  tof_aspim?: number;
  valeur_reconstitution?: number;
  ratio_reconstitution?: number;
  dividende_brut_annuel?: number;
  dividende_net_annuel?: number;
  report_nouveau?: number;
  
  // Patrimoine
  capitalisation?: string;
  nb_associes?: number;
  nb_immeubles?: number;
  surface_totale?: number;
  prix_part?: string;
  prix_part_actuel?: number;
  
  // Données trimestrielles
  trimestre?: string;
  collecte_nette?: string;
  collecte_brute?: string;
  acompte_brut?: number;
  nb_acquisitions?: number;
  montant_acquisitions?: string;
  nb_cessions?: number;
  montant_cessions?: string;
  tof_aspim_trimestre?: number;
  
  // Autres informations
  parts_en_attente?: string;
  parts_echange?: string;
  versement_des_dividendes?: string;
  frais_souscription?: string;
  frais_gestion?: string;
  delai_jouissance?: string;
  minimum_souscription?: string;
  date_prix?: string;
  
  // Événements et actualités
  evenements_cles?: Array<{
    date: string;
    type_evenement: string;
    description: string;
    valeur_avant: string;
    valeur_apres: string;
    variation: string;
  }>;
  
  actualites?: Array<{
    date: string;
    titre: string;
    type_info: string;
    resume: string;
  }>;
}

// SCPIForm reste un sous-composant
const SCPIForm: React.FC<{ scpi: Partial<SCPI> | null, onSave: () => void, onCancel: () => void }> = ({ scpi, onSave, onCancel }) => {
    const [formData, setFormData] = useState({
        name: scpi?.name || '',
        price_per_share: scpi?.price_per_share || 0,
        prix_part_vente: scpi?.prix_part_vente || 0,
        number_of_shares: scpi?.number_of_shares || 0,
        total_value: scpi?.total_value || 0,
        update_date: scpi?.update_date ? new Date(scpi.update_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        scpi_lab_id: scpi?.scpi_lab_id || 0,
        type_scpi: scpi?.type_scpi || '',
        societe_gestion: scpi?.societe_gestion || '',
        tdvm: scpi?.tdvm || 0,
        taux_occupation: scpi?.taux_occupation || 0,
        dividende_trimestriel: scpi?.dividende_trimestriel || 0,
    });

    useEffect(() => {
        // Utiliser le prix de vente s'il est disponible et supérieur à 0, sinon le prix d'achat
        const priceToUse = formData.prix_part_vente && formData.prix_part_vente > 0
            ? formData.prix_part_vente
            : formData.price_per_share;
        const calculatedTotal = priceToUse * formData.number_of_shares;
        if (calculatedTotal !== formData.total_value) {
            setFormData(prev => ({ ...prev, total_value: calculatedTotal }));
        }
    }, [formData.price_per_share, formData.prix_part_vente, formData.number_of_shares, formData.total_value]); // Ajout de prix_part_vente dans les dépendances

    // Mettre à jour le formulaire si la prop scpi change (pour l'édition)
    useEffect(() => {
        setFormData({
            name: scpi?.name || '',
            price_per_share: scpi?.price_per_share || 0,
            prix_part_vente: scpi?.prix_part_vente || 0,
            number_of_shares: scpi?.number_of_shares || 0,
            total_value: scpi?.total_value || 0,
            update_date: scpi?.update_date ? new Date(scpi.update_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
            scpi_lab_id: scpi?.scpi_lab_id || 0,
            type_scpi: scpi?.type_scpi || '',
            societe_gestion: scpi?.societe_gestion || '',
            tdvm: scpi?.tdvm || 0,
            taux_occupation: scpi?.taux_occupation || 0,
            dividende_trimestriel: scpi?.dividende_trimestriel || 0,
        });
    }, [scpi]);


    const handleSubmit = async (e: FormEvent) => {
        e.preventDefault();
        const method = scpi?.id ? 'put' : 'post';
        const url = scpi?.id ? `/scpi/${scpi.id}` : '/scpi';
        try {
            await apiClient[method](url, formData);
            onSave();
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            console.error("Erreur lors de la sauvegarde de la SCPI", errorMessage);
        }
    };

    return (
        <Form onSubmit={handleSubmit}>
            <Form.Group className="mb-3">
                <Form.Label>Nom de la SCPI</Form.Label>
                <Form.Control type="text" value={formData.name} onChange={(e) => setFormData({ ...formData, name: e.target.value })} required />
            </Form.Group>
            <Form.Group className="mb-3">
                <Form.Label>Prix d'achat par part (€)</Form.Label>
                <Form.Control type="number" step="0.01" value={formData.price_per_share} onChange={(e) => setFormData({ ...formData, price_per_share: parseFloat(e.target.value) || 0 })} required />
            </Form.Group>
            <Form.Group className="mb-3">
                <Form.Label>Prix de vente par part (€)</Form.Label>
                <Form.Control
                    type="number"
                    step="0.01"
                    value={formData.prix_part_vente !== undefined && formData.prix_part_vente !== null
                        ? formData.prix_part_vente
                        : formData.price_per_share}
                    onChange={(e) => {
                        // Si le champ est vide, on utilise 0 pour indiquer d'utiliser le prix d'achat
                        const value = e.target.value === '' ? 0 : parseFloat(e.target.value) || 0;
                        setFormData({ ...formData, prix_part_vente: value });
                    }}
                />
                <Form.Text className="text-muted">Si différent du prix d'achat. Laissez vide ou mettez 0 pour utiliser le prix d'achat.</Form.Text>
            </Form.Group>
            <Form.Group className="mb-3">
                <Form.Label>Nombre de parts</Form.Label>
                <Form.Control type="number" value={formData.number_of_shares} onChange={(e) => setFormData({ ...formData, number_of_shares: parseInt(e.target.value) || 0 })} required />
            </Form.Group>
            <Form.Group className="mb-3">
                <Form.Label>Valeur totale (€)</Form.Label>
                <Form.Control type="number" step="0.01" value={formData.total_value} onChange={(e) => setFormData({ ...formData, total_value: parseFloat(e.target.value) || 0 })} required />
                <Form.Text className="text-muted">
                    Calculé automatiquement (prix d'achat) : {(formData.price_per_share * formData.number_of_shares).toFixed(2)} €<br/>
                    Valeur de vente : {((formData.prix_part_vente && formData.prix_part_vente > 0 ? formData.prix_part_vente : formData.price_per_share) * formData.number_of_shares).toFixed(2)} €
                </Form.Text>
            </Form.Group>
            <Form.Group className="mb-3">
                <Form.Label>Date de mise à jour</Form.Label>
                <Form.Control type="date" value={formData.update_date} onChange={(e) => setFormData({ ...formData, update_date: e.target.value })} required />
            </Form.Group>
            <Form.Group className="mb-3">
                <Form.Label>ID SCPI Lab (Optionnel)</Form.Label>
                <Form.Control type="number" value={formData.scpi_lab_id || ''} onChange={(e) => setFormData({ ...formData, scpi_lab_id: parseInt(e.target.value) || 0 })} placeholder="39" />
                <Form.Text className="text-muted">Identifiant de la SCPI sur scpi-lab.com pour récupérer des informations détaillées.</Form.Text>
            </Form.Group>
            <Form.Group className="mb-3">
                <Form.Label>Type de SCPI</Form.Label>
                <Form.Control type="text" value={formData.type_scpi} onChange={(e) => setFormData({ ...formData, type_scpi: e.target.value })} />
                <Form.Text className="text-muted">Type de SCPI (Rendement, Diversifiée, etc.)</Form.Text>
            </Form.Group>
            <Form.Group className="mb-3">
                <Form.Label>Société de Gestion</Form.Label>
                <Form.Control type="text" value={formData.societe_gestion} onChange={(e) => setFormData({ ...formData, societe_gestion: e.target.value })} />
            </Form.Group>
            <Form.Group className="mb-3">
                <Form.Label>TDVM (%)</Form.Label>
                <Form.Control
                    type="number"
                    step="0.01"
                    value={formData.tdvm}
                    onChange={(e) => setFormData({ ...formData, tdvm: parseFloat(e.target.value) || 0 })}
                />
                <Form.Text className="text-muted">
                    Taux de Distribution sur Valeur de Marché
                </Form.Text>
            </Form.Group>
            <Form.Group className="mb-3">
                <Form.Label>Taux d'Occupation (%)</Form.Label>
                <Form.Control
                    type="number"
                    step="0.01"
                    value={formData.taux_occupation}
                    onChange={(e) => setFormData({ ...formData, taux_occupation: parseFloat(e.target.value) || 0 })}
                />
            </Form.Group>
            <Form.Group className="mb-3">
                <Form.Label>Dividende Trimestriel (€)</Form.Label>
                <Form.Control
                    type="number"
                    step="0.01"
                    value={formData.dividende_trimestriel}
                    onChange={(e) => setFormData({ ...formData, dividende_trimestriel: parseFloat(e.target.value) || 0 })}
                />
                <Form.Text className="text-muted">
                    Montant du dividende brut versé trimestriellement par l'ensemble des parts.
                </Form.Text>
            </Form.Group>
            <div className="d-flex justify-content-end gap-2">
                <Button variant="secondary" onClick={onCancel}>Annuler</Button>
                <Button variant="primary" type="submit">Sauvegarder</Button>
            </div>
        </Form>
    );
};

// Renommé en SCPIPage
const SCPIPage: React.FC = () => {
  const [scpis, setSCPIs] = useState<SCPI[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [selectedSCPI, setSelectedSCPI] = useState<Partial<SCPI> | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [scrapedData, setScrapedData] = useState<ScrapedScpiData | null>(null);
  const [scrapingSCPIId, setScrapingSCPIId] = useState<number | null>(null);
  const [isScraping, setIsScraping] = useState(false);
  const [scrapingError, setScrapingError] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isBatchUpdating, setIsBatchUpdating] = useState(false);
  const [batchUpdateResults, setBatchUpdateResults] = useState<any>(null);
  const [updateProgress, setUpdateProgress] = useState<number>(0);
  const [updateTimeout, setUpdateTimeout] = useState<boolean>(false);

  const handleBatchUpdate = async () => {
    setIsBatchUpdating(true);
    setBatchUpdateResults(null);
    setUpdateProgress(0);
    setUpdateTimeout(false);
    
    // Simuler la progression pendant que la requête est en cours
    const progressInterval = setInterval(() => {
      setUpdateProgress(prev => {
        // Augmenter progressivement jusqu'à 90% maximum
        // Le reste sera complété quand la requête sera terminée
        if (prev < 90) {
          return prev + 5;
        }
        return prev;
      });
    }, 3000); // Mettre à jour toutes les 3 secondes
    
    // Définir un timeout de sécurité pour informer l'utilisateur si l'opération prend trop de temps
    const timeoutId = setTimeout(() => {
      setUpdateTimeout(true);
    }, 100000); // 100 secondes
    
    try {
      const response = await apiClient.post('/scpi/update_all');
      console.log("Résultats de la mise à jour en masse:", response.data);
      setBatchUpdateResults(response.data);
      setUpdateProgress(100); // Compléter la progression
      fetchSCPIs(); // Rafraîchir la liste après mise à jour
    } catch (err: any) {
      console.error("Erreur lors de la mise à jour en masse", err);
      if (err.response?.data?.detail) {
        setBatchUpdateResults({
          message: `Erreur: ${err.response.data.detail}`,
          results: []
        });
      } else {
        setBatchUpdateResults({
          message: "Erreur lors de la mise à jour en masse des SCPI",
          results: []
        });
      }
    } finally {
      clearInterval(progressInterval);
      clearTimeout(timeoutId);
      setIsBatchUpdating(false);
    }
  };

  const fetchSCPIs = () => {
    setLoading(true);
    setError(null);
    apiClient.get('/scpi')
      .then(response => {
        setSCPIs(response.data);
        setLoading(false);
        setRetryCount(0);
      })
      .catch(error => {
        console.error('SCPI API error:', error);
        if (retryCount < 2) {
          setRetryCount(prev => prev + 1);
          setTimeout(() => fetchSCPIs(), 1000);
        } else {
          setError('Erreur lors de la récupération des SCPI.');
          setLoading(false);
        }
      });
  };

  useEffect(() => {
    fetchSCPIs();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSave = () => {
    setShowModal(false);
    setSelectedSCPI(null);
    fetchSCPIs();
  };

  const handleDelete = async (id: number) => {
    if (window.confirm("Êtes-vous sûr de vouloir supprimer cette SCPI ?")) {
        try {
            await apiClient.delete(`/scpi/${id}`);
            fetchSCPIs();
        } catch (error) {
            console.error("Erreur lors de la suppression de la SCPI", error);
        }
    }
  };

  const handleScrapeSCPI = async (scpiItem: SCPI) => {
    if (!scpiItem.scpi_lab_id) {
      setScrapingError("Aucun ID SCPI Lab n'est configuré pour cette SCPI.");
      setScrapedData(null);
      setScrapingSCPIId(scpiItem.id);
      return;
    }
    setIsScraping(true);
    setScrapingError(null);
    setScrapedData(null);
    setScrapingSCPIId(scpiItem.id);
    try {
      const response = await apiClient.get(`/scpi/scrape/?scpi_lab_id=${scpiItem.scpi_lab_id}`);
      setScrapedData(response.data);
    } catch (err: any) {
      console.error("Erreur lors du scraping SCPI", err);
      // Gestion améliorée des erreurs
      if (err.response) {
        // Erreur de réponse du serveur
        const statusCode = err.response.status;
        if (statusCode === 404) {
          setScrapingError("SCPI non trouvée sur SCPI Lab. Vérifiez l'ID SCPI Lab.");
        } else if (statusCode === 500) {
          setScrapingError("Erreur serveur lors de la récupération des données. " +
                          (err.response.data?.detail || "Veuillez réessayer plus tard."));
        } else {
          setScrapingError(`Erreur ${statusCode}: ${err.response.data?.detail || "Erreur inconnue"}`);
        }
      } else if (err.request) {
        // Erreur de réseau
        setScrapingError("Impossible de contacter le serveur. Vérifiez votre connexion internet.");
      } else {
        // Autre type d'erreur
        setScrapingError("Erreur lors de la récupération des données: " + err.message);
      }
      setScrapedData(null);
    } finally {
      setIsScraping(false);
    }
  };

  const handleUpdateSCPI = async (scpiItem: SCPI) => {
    if (!scpiItem.scpi_lab_id) {
      setScrapingError("Aucun ID SCPI Lab n'est configuré pour cette SCPI.");
      setScrapingSCPIId(scpiItem.id);
      return;
    }

    setIsUpdating(true);
    setScrapingError(null);
    setScrapingSCPIId(scpiItem.id);

    try {
      const response = await apiClient.post(`/scpi/update/${scpiItem.id}?scpi_lab_id=${scpiItem.scpi_lab_id}`);
      console.log("SCPI mise à jour:", response.data);
      fetchSCPIs(); // Rafraîchir la liste après mise à jour
    } catch (err: any) {
      console.error("Erreur lors de la mise à jour SCPI", err);
      // Gestion améliorée des erreurs
      if (err.response) {
        // Erreur de réponse du serveur
        const statusCode = err.response.status;
        if (statusCode === 404) {
          setScrapingError("SCPI non trouvée. Vérifiez l'ID de la SCPI.");
        } else if (statusCode === 500) {
          // Extraire le message d'erreur détaillé du backend
          const errorDetail = err.response.data?.detail || "";
          if (errorDetail.includes("connection_error")) {
            setScrapingError("Impossible de se connecter à SCPI Lab. Vérifiez votre connexion internet.");
          } else if (errorDetail.includes("scraping_error")) {
            setScrapingError("Structure du site SCPI Lab modifiée. Impossible de récupérer les données.");
          } else if (errorDetail.includes("browser_error")) {
            setScrapingError("Erreur du navigateur lors du scraping. Veuillez réessayer plus tard.");
          } else {
            setScrapingError("Erreur serveur lors de la mise à jour: " + errorDetail);
          }
        } else {
          setScrapingError(`Erreur ${statusCode}: ${err.response.data?.detail || "Erreur inconnue"}`);
        }
      } else if (err.request) {
        // Erreur de réseau
        setScrapingError("Impossible de contacter le serveur. Vérifiez votre connexion internet.");
      } else {
        // Autre type d'erreur
        setScrapingError("Erreur lors de la mise à jour des données: " + err.message);
      }
    } finally {
      setIsUpdating(false);
    }
  };

  if (loading && !showModal && retryCount === 0) return <p>Chargement des SCPI...</p>;
  if (error && retryCount >=2) return (
    <div>
      <p className="text-danger">{error}</p>
      <button className="btn btn-primary" onClick={() => {setRetryCount(0); fetchSCPIs();}}>
        Réessayer
      </button>
    </div>
  );
  if (!loading && !scpis.length && !error) return <p>Aucune SCPI trouvée.</p>;
  if (loading && !showModal) return <p>Chargement des SCPI, tentative {retryCount + 1}...</p>;

  const totalSCPIValue = scpis.reduce((sum, scpiItem) => sum + scpiItem.total_value, 0);
  const totalDividendValue = scpis.reduce((sum, scpiItem) => sum + (scpiItem.dividende_trimestriel || 0), 0);
  
  // Calcul du total des dividendes par SCPI (nombre de parts * dividende trimestriel)
  const calculateTotalDividend = (scpiItem: SCPI) => {
    return (scpiItem.dividende_trimestriel || 0) * scpiItem.number_of_shares;
  };
  
  // Calcul du total général des dividendes
  const totalAllDividends = scpis.reduce((sum, scpiItem) => sum + calculateTotalDividend(scpiItem), 0);
  
  // Calcul de la valeur totale basée sur le prix de vente
  const calculateSaleValue = (scpiItem: SCPI) => {
    // Utiliser prix_part_vente s'il existe et n'est pas 0, sinon price_per_share
    const salePrice = scpiItem.prix_part_vente && scpiItem.prix_part_vente > 0
      ? scpiItem.prix_part_vente
      : scpiItem.price_per_share;
    return salePrice * scpiItem.number_of_shares;
  };
  
  // Calcul du sous-total des valeurs de vente
  const totalSaleValue = scpis.reduce((sum, scpiItem) => sum + calculateSaleValue(scpiItem), 0);

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1>Mes SCPI</h1>
        <div>
          <Button
            variant="success"
            className="me-2"
            onClick={handleBatchUpdate}
            disabled={isBatchUpdating}
          >
            {isBatchUpdating ? 'Mise à jour en cours...' : 'Mettre à jour toutes les SCPI'}
          </Button>
          <Button
            variant="primary"
            onClick={() => { setSelectedSCPI({}); setShowModal(true); }}
          >
            Ajouter une SCPI
          </Button>
        </div>
      </div>
      
      {/* Indicateur de progression pour la mise à jour en masse */}
      {isBatchUpdating && (
        <div className="mb-4">
          <div className="d-flex justify-content-between align-items-center mb-2">
            <strong>Progression de la mise à jour</strong>
            <span>{updateProgress}%</span>
          </div>
          <div className="progress" style={{ height: '25px' }}>
            <div
              className={`progress-bar progress-bar-striped progress-bar-animated ${updateTimeout ? 'bg-warning' : 'bg-info'}`}
              role="progressbar"
              style={{ width: `${updateProgress}%` }}
              aria-valuenow={updateProgress}
              aria-valuemin={0}
              aria-valuemax={100}
            >
              {updateProgress}%
            </div>
          </div>
          {updateTimeout && (
            <div className="alert alert-warning mt-2">
              <i className="bi bi-exclamation-triangle-fill me-2"></i>
              L'opération prend plus de temps que prévu. Veuillez patienter, le serveur traite votre demande...
            </div>
          )}
        </div>
      )}
      
      {batchUpdateResults && (
        <div className={`alert ${batchUpdateResults.message.includes('Erreur') ? 'alert-danger' : 'alert-success'} mb-4`}>
          <h5>{batchUpdateResults.message}</h5>
          {batchUpdateResults.results && batchUpdateResults.results.length > 0 && (
            <ul className="mb-0 mt-2">
              {batchUpdateResults.results.map((result: any, index: number) => (
                <li key={index} className={result.status === 'success' ? 'text-success' : 'text-danger'}>
                  {result.name}: {result.status === 'success' ? 'Mise à jour réussie' : `Échec - ${result.message || 'Erreur inconnue'}`}
                </li>
              ))}
            </ul>
          )}
        </div>
      )}
      { scpis.length > 0 &&
        <table className="table table-striped table-hover">
          <thead className="table-dark">
            <tr>
              <th>Nom</th>
              <th>Société</th>
              <th className="text-end">Prix de vente</th>
              <th className="text-end">Nombre</th>
              <th className="text-end">Valeur vente</th>
              <th className="text-end">TDVM</th>
              <th className="text-end">Dividende/Part</th>
              <th className="text-end">Total Dividende</th>
              <th className="text-center">Date MàJ</th>
              <th className="text-center" style={{ minWidth: '220px' }}>Actions</th>
            </tr>
          </thead>
          <tbody>
            {scpis.map(scpiItem => (
              <React.Fragment key={scpiItem.id}>
                <tr>
                  <td>{scpiItem.name}</td>
                  <td>{scpiItem.societe_gestion || '-'}</td>
                  <td className="text-end">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(
                    scpiItem.prix_part_vente && scpiItem.prix_part_vente > 0
                      ? scpiItem.prix_part_vente
                      : scpiItem.price_per_share
                  )}</td>
                  <td className="text-end">{scpiItem.number_of_shares}</td>
                  <td className="text-end">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(calculateSaleValue(scpiItem))}</td>
                  <td className="text-end">{scpiItem.tdvm ? `${scpiItem.tdvm.toFixed(2)}%` : '-'}</td>
                  <td className="text-end">{scpiItem.dividende_trimestriel ? new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(scpiItem.dividende_trimestriel) : '-'}</td>
                  <td className="text-end">{scpiItem.dividende_trimestriel ? new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(calculateTotalDividend(scpiItem)) : '-'}</td>
                  <td className="text-center">{new Date(scpiItem.update_date).toLocaleDateString('fr-FR')}</td>
                  <td className="text-center">
                    <Button
                      variant="outline-info"
                      size="sm"
                      className="me-1"
                      onClick={() => handleScrapeSCPI(scpiItem)}
                      disabled={isScraping && scrapingSCPIId === scpiItem.id}
                      title="Consulter les données de SCPI Lab"
                    >
                      {isScraping && scrapingSCPIId === scpiItem.id ? 'Chargt...' : 'SCPI Lab'}
                    </Button>
                    <Button
                      variant="outline-success"
                      size="sm"
                      className="me-1"
                      onClick={() => handleUpdateSCPI(scpiItem)}
                      disabled={(isScraping || isUpdating) && scrapingSCPIId === scpiItem.id}
                      title="Mettre à jour automatiquement les données depuis SCPI Lab"
                    >
                      {isUpdating && scrapingSCPIId === scpiItem.id ? 'MàJ...' : 'MàJ Auto'}
                    </Button>
                    <Button variant="outline-primary" size="sm" className="me-1" onClick={() => { setSelectedSCPI(scpiItem); setShowModal(true); }}>Modifier</Button>
                    <Button variant="outline-danger" size="sm" onClick={() => handleDelete(scpiItem.id)}>Supprimer</Button>
                  </td>
                </tr>
                {scrapingSCPIId === scpiItem.id && (
                  <tr>
                    <td colSpan={10}>
                      {isScraping && <p className="text-info m-2">Chargement des données depuis SCPI Lab...</p>}
                      {isUpdating && <p className="text-info m-2">Mise à jour des données depuis SCPI Lab...</p>}
                      {scrapingError && <p className="text-danger m-2">{scrapingError}</p>}
                      {scrapedData && !isScraping && (
                        <div className="p-3 my-2 bg-light border rounded">
                          <h5 className="mb-3">Détails de {scrapedData.nom || scpiItem.name} (SCPI Lab)</h5>
                          
                          {/* Onglets pour organiser les différentes catégories d'informations */}
                          <Tabs defaultActiveKey="general" className="mb-3">
                            {/* Onglet Informations générales */}
                            <Tab eventKey="general" title="Informations générales">
                              <div className="row">
                                {/* Première colonne - Profil SCPI */}
                                {(scrapedData.societe_gestion || scrapedData.type_capital || scrapedData.type_actifs ||
                                  scrapedData.localisation_principale || scrapedData.statut || scrapedData.annee_creation) && (
                                  <div className="col-md-6">
                                    <div className="card h-100">
                                      <div className="card-header bg-primary text-white">
                                        <h6 className="mb-0">Profil SCPI</h6>
                                      </div>
                                      <div className="card-body">
                                        <ul className="list-group list-group-flush">
                                          {scrapedData.societe_gestion && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Société de gestion:</strong> <span>{scrapedData.societe_gestion}</span>
                                            </li>
                                          )}
                                          {scrapedData.type_capital && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Type de capital:</strong> <span>{scrapedData.type_capital}</span>
                                            </li>
                                          )}
                                          {scrapedData.type_actifs && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Type d'actifs:</strong> <span>{scrapedData.type_actifs}</span>
                                            </li>
                                          )}
                                          {scrapedData.localisation_principale && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Localisation principale:</strong> <span>{scrapedData.localisation_principale}</span>
                                            </li>
                                          )}
                                          {scrapedData.statut && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Statut:</strong> <span>{scrapedData.statut}</span>
                                            </li>
                                          )}
                                          {scrapedData.annee_creation && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Année de création:</strong> <span>{scrapedData.annee_creation}</span>
                                            </li>
                                          )}
                                        </ul>
                                      </div>
                                    </div>
                                  </div>
                                )}
                                
                                {/* Deuxième colonne - Chiffres clés */}
                                {(scrapedData.prix_part || scrapedData.capitalisation || scrapedData.nb_associes ||
                                  scrapedData.nb_immeubles || scrapedData.surface_totale) && (
                                  <div className="col-md-6">
                                    <div className="card h-100">
                                      <div className="card-header bg-success text-white">
                                        <h6 className="mb-0">Chiffres clés</h6>
                                      </div>
                                      <div className="card-body">
                                        <ul className="list-group list-group-flush">
                                          {scrapedData.prix_part && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Prix de la part:</strong> <span>{scrapedData.prix_part}</span>
                                            </li>
                                          )}
                                          {scrapedData.capitalisation && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Capitalisation:</strong> <span>{scrapedData.capitalisation}</span>
                                            </li>
                                          )}
                                          {scrapedData.nb_associes && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Nombre d'associés:</strong> <span>{scrapedData.nb_associes}</span>
                                            </li>
                                          )}
                                          {scrapedData.nb_immeubles && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Nombre d'immeubles:</strong> <span>{scrapedData.nb_immeubles}</span>
                                            </li>
                                          )}
                                          {scrapedData.surface_totale && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Surface totale:</strong> <span>{scrapedData.surface_totale.toLocaleString('fr-FR')} m²</span>
                                            </li>
                                          )}
                                        </ul>
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </Tab>
                            
                            {/* Onglet Performance */}
                            <Tab eventKey="performance" title="Performance">
                              <div className="row">
                                {/* Première colonne - Distribution */}
                                {(scrapedData.tdvm || scrapedData.taux_distribution_brut || scrapedData.taux_distribution_net ||
                                  scrapedData.dividende_brut_annuel || scrapedData.dividende_net_annuel || scrapedData.report_nouveau) && (
                                  <div className="col-md-6">
                                    <div className="card h-100">
                                      <div className="card-header bg-info text-white">
                                        <h6 className="mb-0">Distribution</h6>
                                      </div>
                                      <div className="card-body">
                                        <ul className="list-group list-group-flush">
                                          {scrapedData.tdvm && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>TDVM:</strong> <span>{scrapedData.tdvm}</span>
                                            </li>
                                          )}
                                          {scrapedData.taux_distribution_brut && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Taux distribution brut:</strong> <span>{scrapedData.taux_distribution_brut.toFixed(2)}%</span>
                                            </li>
                                          )}
                                          {scrapedData.taux_distribution_net && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Taux distribution net:</strong> <span>{scrapedData.taux_distribution_net.toFixed(2)}%</span>
                                            </li>
                                          )}
                                          {scrapedData.dividende_brut_annuel && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Dividende brut annuel:</strong> <span>{scrapedData.dividende_brut_annuel.toFixed(2)} €</span>
                                            </li>
                                          )}
                                          {scrapedData.dividende_net_annuel && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Dividende net annuel:</strong> <span>{scrapedData.dividende_net_annuel.toFixed(2)} €</span>
                                            </li>
                                          )}
                                          {scrapedData.report_nouveau && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Report à nouveau:</strong> <span>{scrapedData.report_nouveau.toFixed(2)} €</span>
                                            </li>
                                          )}
                                        </ul>
                                      </div>
                                    </div>
                                  </div>
                                )}
                                
                                {/* Deuxième colonne - Valorisation */}
                                {(scrapedData.taux_occupation || scrapedData.tof_aspim ||
                                  scrapedData.valeur_reconstitution || scrapedData.ratio_reconstitution) && (
                                  <div className="col-md-6">
                                    <div className="card h-100">
                                      <div className="card-header bg-warning text-dark">
                                        <h6 className="mb-0">Valorisation et Occupation</h6>
                                      </div>
                                      <div className="card-body">
                                        <ul className="list-group list-group-flush">
                                          {scrapedData.taux_occupation && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Taux d'occupation:</strong> <span>{scrapedData.taux_occupation}</span>
                                            </li>
                                          )}
                                          {scrapedData.tof_aspim && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>TOF ASPIM:</strong> <span>{scrapedData.tof_aspim.toFixed(2)}%</span>
                                            </li>
                                          )}
                                          {scrapedData.valeur_reconstitution && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Valeur de reconstitution:</strong> <span>{scrapedData.valeur_reconstitution.toFixed(2)} €</span>
                                            </li>
                                          )}
                                          {scrapedData.ratio_reconstitution && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Ratio de reconstitution:</strong> <span>{scrapedData.ratio_reconstitution.toFixed(2)}%</span>
                                            </li>
                                          )}
                                        </ul>
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </Tab>
                            
                            {/* Onglet Informations trimestrielles */}
                            <Tab eventKey="trimestre" title="Informations trimestrielles">
                              <div className="row">
                                {/* Première colonne - Collecte et distribution */}
                                {(scrapedData.collecte_brute || scrapedData.collecte_nette ||
                                  scrapedData.acompte_brut || scrapedData.tof_aspim_trimestre || scpiItem.dividende_trimestriel) && (
                                  <div className="col-md-6">
                                    <div className="card h-100">
                                      <div className="card-header bg-primary text-white">
                                        <h6 className="mb-0">Collecte et Distribution {scrapedData.trimestre && `(${scrapedData.trimestre})`}</h6>
                                      </div>
                                      <div className="card-body">
                                        <ul className="list-group list-group-flush">
                                          {scpiItem.dividende_trimestriel && (
                                            <li className="list-group-item d-flex justify-content-between bg-light">
                                              <strong>Dividende trimestriel:</strong>
                                              <span className="text-success fw-bold">
                                                {new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(scpiItem.dividende_trimestriel)}
                                              </span>
                                            </li>
                                          )}
                                          {scrapedData.collecte_brute && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Collecte brute:</strong> <span>{scrapedData.collecte_brute}</span>
                                            </li>
                                          )}
                                          {scrapedData.collecte_nette && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Collecte nette:</strong> <span>{scrapedData.collecte_nette}</span>
                                            </li>
                                          )}
                                          {scrapedData.acompte_brut && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Acompte brut:</strong> <span>{scrapedData.acompte_brut.toFixed(2)} €</span>
                                            </li>
                                          )}
                                          {scrapedData.tof_aspim_trimestre && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>TOF ASPIM trimestre:</strong> <span>{scrapedData.tof_aspim_trimestre.toFixed(2)}%</span>
                                            </li>
                                          )}
                                        </ul>
                                      </div>
                                    </div>
                                  </div>
                                )}
                                
                                {/* Deuxième colonne - Transactions */}
                                {(scrapedData.nb_acquisitions || scrapedData.montant_acquisitions ||
                                  scrapedData.nb_cessions || scrapedData.montant_cessions) && (
                                  <div className="col-md-6">
                                    <div className="card h-100">
                                      <div className="card-header bg-success text-white">
                                        <h6 className="mb-0">Transactions immobilières</h6>
                                      </div>
                                      <div className="card-body">
                                        <ul className="list-group list-group-flush">
                                          {scrapedData.nb_acquisitions && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Nombre d'acquisitions:</strong> <span>{scrapedData.nb_acquisitions}</span>
                                            </li>
                                          )}
                                          {scrapedData.montant_acquisitions && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Montant acquisitions:</strong> <span>{scrapedData.montant_acquisitions}</span>
                                            </li>
                                          )}
                                          {scrapedData.nb_cessions && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Nombre de cessions:</strong> <span>{scrapedData.nb_cessions}</span>
                                            </li>
                                          )}
                                          {scrapedData.montant_cessions && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Montant cessions:</strong> <span>{scrapedData.montant_cessions}</span>
                                            </li>
                                          )}
                                        </ul>
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </Tab>
                            
                            {/* Onglet Informations complémentaires */}
                            <Tab eventKey="complementaire" title="Informations complémentaires">
                              <div className="row">
                                {/* Conditions de souscription */}
                                {(scrapedData.minimum_souscription || scrapedData.frais_souscription ||
                                  scrapedData.frais_gestion || scrapedData.delai_jouissance) && (
                                  <div className="col-md-6">
                                    <div className="card h-100">
                                      <div className="card-header bg-info text-white">
                                        <h6 className="mb-0">Conditions de souscription</h6>
                                      </div>
                                      <div className="card-body">
                                        <ul className="list-group list-group-flush">
                                          {scrapedData.minimum_souscription && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Minimum souscription:</strong> <span>{scrapedData.minimum_souscription}</span>
                                            </li>
                                          )}
                                          {scrapedData.frais_souscription && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Frais souscription:</strong> <span>{scrapedData.frais_souscription}</span>
                                            </li>
                                          )}
                                          {scrapedData.frais_gestion && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Frais de gestion:</strong> <span>{scrapedData.frais_gestion}</span>
                                            </li>
                                          )}
                                          {scrapedData.delai_jouissance && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Délai de jouissance:</strong> <span>{scrapedData.delai_jouissance}</span>
                                            </li>
                                          )}
                                        </ul>
                                      </div>
                                    </div>
                                  </div>
                                )}
                                
                                {/* Marché des parts */}
                                {(scrapedData.parts_en_attente || scrapedData.parts_echange ||
                                  scrapedData.versement_des_dividendes || scrapedData.date_prix) && (
                                  <div className="col-md-6">
                                    <div className="card h-100">
                                      <div className="card-header bg-warning text-dark">
                                        <h6 className="mb-0">Marché des parts</h6>
                                      </div>
                                      <div className="card-body">
                                        <ul className="list-group list-group-flush">
                                          {scrapedData.parts_en_attente && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Parts en attente:</strong> <span>{scrapedData.parts_en_attente}</span>
                                            </li>
                                          )}
                                          {scrapedData.parts_echange && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Parts échangées:</strong> <span>{scrapedData.parts_echange}</span>
                                            </li>
                                          )}
                                          {scrapedData.versement_des_dividendes && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Versement des dividendes:</strong> <span>{scrapedData.versement_des_dividendes}</span>
                                            </li>
                                          )}
                                          {scrapedData.date_prix && (
                                            <li className="list-group-item d-flex justify-content-between">
                                              <strong>Date prix:</strong> <span>{scrapedData.date_prix}</span>
                                            </li>
                                          )}
                                        </ul>
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </Tab>
                          </Tabs>
                        </div>
                      )}
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
          <tfoot className="table-dark">
            <tr>
              <td colSpan={4} className="text-end fw-bold">Sous-total:</td>
              <td className="text-end fw-bold">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(totalSaleValue)}</td>
              <td></td>
              <td className="text-end fw-bold">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(totalDividendValue)}</td>
              <td className="text-end fw-bold">{new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(totalAllDividends)}</td>
              <td colSpan={2}></td>
            </tr>
          </tfoot>
        </table>
      }

      <Modal show={showModal} onHide={() => { setShowModal(false); setSelectedSCPI(null); }} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>{selectedSCPI?.id ? `Modifier ${selectedSCPI.name}` : 'Ajouter une SCPI'}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <SCPIForm scpi={selectedSCPI} onSave={handleSave} onCancel={() => { setShowModal(false); setSelectedSCPI(null); }} />
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default SCPIPage;