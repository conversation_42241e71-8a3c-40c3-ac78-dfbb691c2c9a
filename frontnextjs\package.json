{"name": "frontnextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"axios": "^1.10.0", "bootstrap": "^5.3.7", "chart.js": "^4.5.0", "chartjs-plugin-annotation": "^3.1.0", "next": "^15.3.5", "react": "^19.1.0", "react-bootstrap": "^2.10.10", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5"}}