# Architecture Système - Fire UI

## Architecture du projet (Mise à jour Décembre 2024)
Le projet Fire UI est structuré en deux parties principales :
- **Frontend Principal** : Développé avec **Next.js 15 + React 19**, situé dans le dossier `frontnextjs/`.
- **Frontend Archivé** : Ancien React CRA dans `frontend/` (conservé pour référence).
- **Backend** : Développé avec **Python** et **FastAPI**, situé dans le dossier `backend/`.

## Relations entre composants
- Le frontend NextJS communique avec le backend via des API RESTful (Axios).
- Les données financières saisies par l'utilisateur sont envoyées au backend pour traitement et stockage.
- Les résultats des simulations sont renvoyés au frontend pour affichage.
- Navigation gérée par Next.js App Router avec pages dédiées.

## Choix technologiques (Actualisés)
- **Frontend** : **Next.js 15** avec App Router pour SSR/SSG, optimisations et meilleure structure.
- **Styling** : **Bootstrap 5** + **Tailwind CSS v3** pour interface responsive.
- **Charts** : **Chart.js** + **react-chartjs-2** pour visualisations financières.
- **Backend** : **FastAPI** pour sa simplicité, performances et intégration Pydantic.
- **Database** : **SQLite** pour stockage local des données financières.

## Organisation des fichiers (NextJS)
- `frontnextjs/src/app/` : Pages Next.js avec App Router (layout.tsx, page.tsx par route).
- `frontnextjs/src/components/` : Composants React réutilisables et logique métier.
- `backend/` : Modules Python pour API, modèles de données et logique backend.

## Points critiques d'implémentation
- **Bootstrap + NextJS** : Chargement JavaScript dynamique via BootstrapClient.tsx.
- **CSS Management** : Ordre d'import crucial (Bootstrap avant Tailwind).
- **Responsive Design** : Classes Bootstrap `d-lg-*` pour affichage conditionnel.
- **API Communication** : Axios configuré pour communication frontend/backend.
- **Theme Management** : CSS variables forcées pour éviter mode sombre automatique.

## Patterns Architecturaux Découverts

### Pattern : Bootstrap JavaScript Loading
```typescript
// BootstrapClient.tsx - Chargement dynamique côté client
'use client';
import { useEffect } from 'react';

export default function BootstrapClient() {
  useEffect(() => {
    import('bootstrap/dist/js/bootstrap.bundle.min.js');
  }, []);
  return null;
}
```

### Pattern : Navbar Responsive NextJS
```typescript
// Affichage conditionnel desktop/mobile
<div className="navbar-nav-wrapper d-lg-flex">
  {/* Desktop navigation */}
</div>
{isMenuOpen && (
  <div className="navbar-collapse show d-lg-none">
    {/* Mobile navigation */}
  </div>
)}
```

### Pattern : CSS Theme Override
```css
/* Force light theme, disable auto dark mode */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #ffffff !important;
    --foreground: #212529 !important;
  }
}
```

### Pattern : NextJS App Router Structure
```
frontnextjs/src/app/
├── layout.tsx          # Root layout with navbar
├── page.tsx           # Dashboard (/)
├── assets/page.tsx    # Patrimoine (/assets)
├── scpi/page.tsx      # SCPI (/scpi)
├── liabilities/page.tsx # Emprunts (/liabilities)
└── [other-routes]/    # Other pages
```
