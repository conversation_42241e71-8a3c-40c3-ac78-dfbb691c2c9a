**Rôle :** Tu es un développeur full-stack expérimenté, spécialisé dans la création d'applications financières (FinTech).

**Mission :** Concevoir et développer une application web de gestion de patrimoine personnel. L'application doit être sécurisée, performante, intuitive et esthétiquement professionnelle. L'ensemble des données sera géré localement via une base de données SQLite ou DuckDB.

**Cahier des Charges Détaillé :**

**1. Dashboard (Vue d'ensemble)**

- **Indicateurs Clés (KPIs) :**
    
    - `Patrimoine Net Total` : Calculé en temps réel (Total Actifs - Total Passifs).
        
    - `Performance Mensuelle / Annuelle` : Évolution en % et en valeur.
        
- **Visualisations :**
    
    - **Graphique d'Allocation (Donut Chart) :** Répartition du portefeuille selon les catégories définies, avec des pourcentages cliquables pour filtrer la vue.
        
    - **Graphique d'Évolution Historique (Line Chart) :** Montre la croissance du patrimoine net sur le temps (vue mensuelle, annuelle, globale).
        
    - **Jauge de Progression FIRE :** Barre de progression claire indiquant `Patrimoine Actuel / Objectif FIRE`.
        

**2. Module de Gestion des Actifs ("Mon Patrimoine")**

- **Structure des comptes :** Chaque actif doit avoir un nom, une valeur, une date de mise à jour et être assigné à une catégorie.
    
- **Catégories d'actifs :**
    
    - `Liquidités` (Cash)
        
    - `Bourse` (Actions, ETFs, hors immobilier)
        
    - `Immobilier` (SCPI, investissement locatif, actions foncières)
        
    - `Fonds Sécurisés` (Fonds en euros, Obligations)
        
    - `Prêts Participatifs` (Crowdlending / Crowdfunding)
        
    - `Crypto-actifs` (Cryptomonnaies)
        
- **Fonctionnalités SCPI :** Pour un actif de type SCPI, permettre de lier un ou plusieurs emprunts.
    
- **Interface :** Une vue tableau affichant tous les actifs, avec des fonctions de tri, de recherche et la possibilité d'ajouter/modifier/supprimer chaque ligne.
    

**3. Module de Gestion des Passifs ("Mes Emprunts")**

- Permettre de créer, suivre et mettre à jour les emprunts.
    
- **Champs requis :** Nom de l'emprunt, montant initial, capital restant dû, taux d'intérêt, date de fin, mensualité.
    
- Le total des passifs est soustrait au total des actifs pour le calcul du patrimoine net.
    

**4. Module "Objectif FIRE" (Financial Independence, Retire Early)**

- **Configuration :** L'utilisateur doit pouvoir définir son "Numéro FIRE" (montant à atteindre) et son taux de retrait sécurisé (ex: 4%).
    
- **Calculs :**
    
    - Affichage du montant restant à investir.
        
    - Estimation du revenu passif potentiel basé sur le patrimoine actuel et le taux de retrait.
        
    - Projection de la date d'atteinte de l'objectif basée sur le taux d'épargne et la performance moyenne (fonctionnalité avancée).
        

**5. Exigences Techniques et UX**

- **Base de données :** Utiliser **SQLite** pour la simplicité ou **DuckDB** pour l'analyse de données performante. Le fichier de la base de données doit être stocké localement.
    
- **Interface :** Design épuré, moderne et responsive (utilisable sur ordinateur et mobile).
    
- **Graphiques :** Utiliser une librairie comme Chart.js, D3.js ou ECharts pour des graphiques interactifs et esthétiques.