from pydantic import BaseModel, Field
from datetime import date
from typing import List, Optional

class LiabilityBase(BaseModel):
    name: str
    initial_amount: float
    remaining_capital: float
    interest_rate: float
    end_date: date
    monthly_payment: float
    asset_id: Optional[int] = None

class LiabilityCreate(LiabilityBase):
    pass

class Liability(LiabilityBase):
    id: int

    class Config:
        from_attributes = True

class CategoryValue(BaseModel):
    name: str
    value: float

class AssetCategorySchema(BaseModel):
    category_name: str
    value: float

    class Config:
        from_attributes = True

class AssetBase(BaseModel):
    name: str
    value: float
    annual_interest: Optional[float] = None
    notes: Optional[str] = None
    update_date: date

class AssetCreate(AssetBase):
    categories: List[CategoryValue]

class Asset(AssetBase):
    id: int
    liabilities: List[Liability] = []
    categories: List[AssetCategorySchema] = []

    class Config:
        from_attributes = True

class PatrimoineHistoryBase(BaseModel):
    date: date
    net_patrimoine: float

class PatrimoineHistoryCreate(PatrimoineHistoryBase):
    pass

class PatrimoineHistory(PatrimoineHistoryBase):
    id: int

    class Config:
        from_attributes = True

class SCPIBase(BaseModel):
    name: str
    price_per_share: float
    number_of_shares: int
    total_value: float
    update_date: date
    scpi_lab_id: Optional[int] = None
    dividende_trimestriel: Optional[float] = None
    prix_part_vente: Optional[float] = None  # Ajout du champ manquant
    
    # Informations générales
    type_scpi: Optional[str] = None
    societe_gestion: Optional[str] = None
    statut: Optional[str] = None
    type_capital: Optional[str] = None
    type_actifs: Optional[str] = None
    localisation_principale: Optional[str] = None
    annee_creation: Optional[int] = None
    
    # Performance et valorisation
    tdvm: Optional[float] = None
    taux_distribution_net: Optional[float] = None
    taux_occupation: Optional[float] = None
    valeur_reconstitution: Optional[float] = None
    ratio_reconstitution: Optional[float] = None
    
    # Patrimoine
    nb_immeubles: Optional[int] = None
    surface_totale: Optional[int] = None
    
    # Données trimestrielles
    trimestre_courant: Optional[str] = None
    collecte_brute: Optional[str] = None
    collecte_nette: Optional[str] = None
    acompte_dernier_trimestre: Optional[float] = None
    
    # Métadonnées
    derniere_maj: Optional[date] = None
    date_extraction: Optional[date] = None

class SCPICreate(SCPIBase):
    pass

class SCPI(SCPIBase):
    id: int
    # primaliance_url is already in SCPIBase, so it will be inherited here

    class Config:
        from_attributes = True

class FireSettingsBase(BaseModel):
    fire_target_amount: float
    secure_withdrawal_rate: float
    update_date: date

class FireSettingsCreate(FireSettingsBase):
    pass

class FireSettingsUpdate(BaseModel):
    fire_target_amount: Optional[float] = None
    secure_withdrawal_rate: Optional[float] = None

class FireSettings(FireSettingsBase):
    id: int

    class Config:
        from_attributes = True

class PatrimoineEvolutionBase(BaseModel):
    annee: int
    investissement: float
    prix_part_scpi: float
    remboursement_credit: float
    valeur_reelle_scpi: float
    total_patrimoine: float
    evolution_pourcentage: Optional[float] = None
    evolution_euros: Optional[float] = None
    croissance_moyenne: Optional[float] = None
    tcam: Optional[float] = None

class PatrimoineEvolutionCreate(PatrimoineEvolutionBase):
    pass

class PatrimoineEvolutionUpdate(BaseModel):
    annee: Optional[int] = None
    investissement: Optional[float] = None
    prix_part_scpi: Optional[float] = None
    remboursement_credit: Optional[float] = None
    valeur_reelle_scpi: Optional[float] = None
    total_patrimoine: Optional[float] = None
    evolution_pourcentage: Optional[float] = None
    evolution_euros: Optional[float] = None
    croissance_moyenne: Optional[float] = None
    tcam: Optional[float] = None

class PatrimoineEvolution(PatrimoineEvolutionBase):
    id: int

    class Config:
        from_attributes = True

# Budget & Dépenses Schemas
class BudgetCategoryBase(BaseModel):
    nom: str
    budget_annuel: float
    description: Optional[str] = None
    ordre_affichage: Optional[int] = 0

class BudgetCategoryCreate(BudgetCategoryBase):
    pass

class BudgetCategoryUpdate(BaseModel):
    nom: Optional[str] = None
    budget_annuel: Optional[float] = None
    description: Optional[str] = None
    ordre_affichage: Optional[int] = None

class BudgetCategory(BudgetCategoryBase):
    id: int

    class Config:
        from_attributes = True

class DepenseReelleBase(BaseModel):
    categorie_id: int
    montant: float
    date_depense: date
    description: Optional[str] = None

class DepenseReelleCreate(DepenseReelleBase):
    pass

class DepenseReelleUpdate(BaseModel):
    categorie_id: Optional[int] = None
    montant: Optional[float] = None
    date_depense: Optional[date] = None
    description: Optional[str] = None

class DepenseReelle(DepenseReelleBase):
    id: int
    mois: int
    annee: int

    class Config:
        from_attributes = True

# Schémas pour les analyses budgétaires
class BudgetAnalysis(BaseModel):
    categorie_id: int
    nom_categorie: str
    budget_annuel: float
    depenses_mois_actuel: float
    depenses_annee_actuelle: float
    ecart_euros: float
    ecart_pourcentage: float
    projection_annuelle: float
    statut: str  # "ok", "attention", "depassement"

class BudgetSummary(BaseModel):
    total_budget_annuel: float
    retrait_brut_necessaire: float  # Budget + 30% impôts/PS
    impact_date_fire: str  # Information FIRE

# Schemas for FireAllocationTarget
class FireAllocationTargetBase(BaseModel):
    category_key: str = Field(..., examples=["Liquidité"])
    target_percentage: float = Field(..., examples=[2.5])

class FireAllocationTargetCreate(FireAllocationTargetBase):
    pass

class FireAllocationTargetUpdate(BaseModel):
    target_percentage: Optional[float] = None

class FireAllocationTargetInDB(FireAllocationTargetBase):
    id: int

    class Config:
        from_attributes = True
