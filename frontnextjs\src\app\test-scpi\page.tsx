'use client';

import React, { useEffect, useState } from 'react';

interface SCPI {
  id: number;
  name: string;
  price_per_share: number;
  number_of_shares: number;
  total_value: number;
  dividende_trimestriel: number;
  prix_part_vente: number;
}

export default function TestSCPI() {
  const [scpis, setSCPIs] = useState<SCPI[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSCPIs = async () => {
      try {
        console.log('Fetching SCPI data...');
        const response = await fetch('http://localhost:8000/api/scpi/');
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('SCPI data received:', data);
        setSCPIs(data);
      } catch (err) {
        console.error('Error fetching SCPI data:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchSCPIs();
  }, []);

  if (loading) {
    return <div className="container mt-4">
      <h1>Test SCPI - Chargement...</h1>
    </div>;
  }

  if (error) {
    return <div className="container mt-4">
      <h1>Test SCPI - Erreur</h1>
      <div className="alert alert-danger">
        Erreur: {error}
      </div>
    </div>;
  }

  const totalValue = scpis.reduce((sum, scpi) => sum + scpi.total_value, 0);
  const totalDividends = scpis.reduce((sum, scpi) => sum + (scpi.dividende_trimestriel * scpi.number_of_shares), 0);
  const totalShares = scpis.reduce((sum, scpi) => sum + scpi.number_of_shares, 0);

  return (
    <div className="container mt-4">
      <h1>Test SCPI - Données Corrigées</h1>
      
      <div className="table-responsive">
        <table className="table table-striped">
          <thead>
            <tr>
              <th>SCPI</th>
              <th>Prix (€)</th>
              <th>Nombre de parts</th>
              <th>Valeur totale (€)</th>
              <th>Acompte brut T1-2025 (€/part)</th>
              <th>Dividende trimestriel total (€)</th>
            </tr>
          </thead>
          <tbody>
            {scpis.map((scpi) => (
              <tr key={scpi.id}>
                <td>{scpi.name}</td>
                <td>{scpi.price_per_share.toFixed(2)}</td>
                <td>{scpi.number_of_shares}</td>
                <td>{scpi.total_value.toFixed(2)}</td>
                <td>{scpi.dividende_trimestriel.toFixed(2)}</td>
                <td>{(scpi.dividende_trimestriel * scpi.number_of_shares).toFixed(2)}</td>
              </tr>
            ))}
          </tbody>
          <tfoot>
            <tr className="table-info">
              <th>TOTAL PORTEFEUILLE</th>
              <th>-</th>
              <th>{totalShares}</th>
              <th>{totalValue.toFixed(2)}</th>
              <th>-</th>
              <th>{totalDividends.toFixed(2)}</th>
            </tr>
          </tfoot>
        </table>
      </div>

      <div className="mt-4">
        <h3>Vérification des données de référence</h3>
        <div className="row">
          <div className="col-md-6">
            <div className="card">
              <div className="card-body">
                <h5 className="card-title">Valeurs calculées</h5>
                <ul className="list-group list-group-flush">
                  <li className="list-group-item">Valeur totale: {totalValue.toFixed(2)}€</li>
                  <li className="list-group-item">Nombre total de parts: {totalShares}</li>
                  <li className="list-group-item">Dividendes trimestriels totaux: {totalDividends.toFixed(2)}€</li>
                </ul>
              </div>
            </div>
          </div>
          <div className="col-md-6">
            <div className="card">
              <div className="card-body">
                <h5 className="card-title">Valeurs attendues</h5>
                <ul className="list-group list-group-flush">
                  <li className="list-group-item">Valeur totale: 81,483.11€</li>
                  <li className="list-group-item">Nombre total de parts: 275</li>
                  <li className="list-group-item">Dividendes trimestriels totaux: 1,033.05€</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mt-3">
          {Math.abs(totalValue - 81483.11) < 1 ? (
            <div className="alert alert-success">✅ Valeur totale correcte</div>
          ) : (
            <div className="alert alert-danger">❌ Écart sur la valeur totale</div>
          )}
          
          {Math.abs(totalDividends - 1033.05) < 1 ? (
            <div className="alert alert-success">✅ Dividendes corrects</div>
          ) : (
            <div className="alert alert-danger">❌ Écart sur les dividendes</div>
          )}
        </div>
      </div>
    </div>
  );
}
