import React from 'react'; // React pour JSX dans le Server Component
import { AssetsClientPart, Asset } from './AssetsClientPart'; // Importer le Client Component et le type Asset

// --- Server Component (Page principale) ---
async function getAssetsSsr(): Promise<Asset[] | null> {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api'}/assets`, {
      cache: 'no-store' // Pour s'assurer que les données sont fraîches à chaque chargement
    });
    if (!response.ok) {
      console.error("SSR Fetch Error (Assets):", response.status, response.statusText);
      const errorText = await response.text();
      console.error("SSR Fetch Error Body (Assets):", errorText);
      return null;
    }
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("SSR Fetch Exception (Assets):", error);
    return null;
  }
}

export default async function AssetsPage() {
  const initialAssets = await getAssetsSsr();
  let fetchErrorOccurred;

  if (initialAssets === null) {
    fetchErrorOccurred = "Erreur lors du chargement des actifs côté serveur.";
  }

  return (
    <AssetsClientPart
      initialAssets={initialAssets || []}
      fetchError={fetchErrorOccurred}
    />
  );
}
// --- Fin du Server Component ---
